"use client"

import { useEffect, useRef } from "react"
import { Line } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  type ChartOptions,
} from "chart.js"

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend)

interface VibrationData {
  timestamp: string
  vibration_x: number
  vibration_y: number
  vibration_z: number
  etat_machine: string
}

interface VibrationChartProps {
  data: VibrationData[]
  realTimeData?: VibrationData
  axis?: "x" | "y" | "z" | "all"
  height?: number
}

export function VibrationChart({ data, realTimeData, axis = "all", height = 400 }: VibrationChartProps) {
  const chartRef = useRef<ChartJS<"line">>(null)

  // Combiner les données historiques avec les données temps réel
  const combinedData = [...data]
  if (realTimeData) {
    combinedData.push(realTimeData)
  }

  const labels = combinedData.map((d) => new Date(d.timestamp).toLocaleTimeString())

  const datasets = []

  if (axis === "all" || axis === "x") {
    datasets.push({
      label: "Vibration X",
      data: combinedData.map((d) => d.vibration_x),
      borderColor: "rgb(239, 68, 68)",
      backgroundColor: "rgba(239, 68, 68, 0.1)",
      tension: 0.4,
      fill: axis !== "all",
    })
  }

  if (axis === "all" || axis === "y") {
    datasets.push({
      label: "Vibration Y",
      data: combinedData.map((d) => d.vibration_y),
      borderColor: "rgb(34, 197, 94)",
      backgroundColor: "rgba(34, 197, 94, 0.1)",
      tension: 0.4,
      fill: axis !== "all",
    })
  }

  if (axis === "all" || axis === "z") {
    datasets.push({
      label: "Vibration Z",
      data: combinedData.map((d) => d.vibration_z),
      borderColor: "rgb(59, 130, 246)",
      backgroundColor: "rgba(59, 130, 246, 0.1)",
      tension: 0.4,
      fill: axis !== "all",
    })
  }

  const options: ChartOptions<"line"> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
      tooltip: {
        mode: "index",
        intersect: false,
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: "Temps",
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: "Vibration (mm/s)",
        },
        beginAtZero: true,
      },
    },
    interaction: {
      mode: "nearest",
      axis: "x",
      intersect: false,
    },
    elements: {
      point: {
        radius: 2,
        hoverRadius: 6,
      },
    },
    animation: {
      duration: realTimeData ? 750 : 0,
    },
  }

  // Ligne de seuil d'alerte
  const alertThreshold = 2.0

  useEffect(() => {
    const chart = chartRef.current
    if (chart) {
      // Ajouter une ligne horizontale pour le seuil d'alerte
      const ctx = chart.ctx
      const yAxis = chart.scales.y
      const xAxis = chart.scales.x

      ctx.save()
      ctx.strokeStyle = "rgba(255, 193, 7, 0.8)"
      ctx.lineWidth = 2
      ctx.setLineDash([5, 5])
      ctx.beginPath()
      ctx.moveTo(xAxis.left, yAxis.getPixelForValue(alertThreshold))
      ctx.lineTo(xAxis.right, yAxis.getPixelForValue(alertThreshold))
      ctx.stroke()
      ctx.restore()
    }
  })

  return (
    <div style={{ height: `${height}px` }}>
      <Line ref={chartRef} data={{ labels, datasets }} options={options} />
    </div>
  )
}
