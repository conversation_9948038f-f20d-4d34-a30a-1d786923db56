"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { TandemViewer } from "@/components/tandem-viewer"
import { useRealTimeData } from "@/hooks/use-real-time-data"
import { useTandemSync } from "@/hooks/use-tandem-sync"
import { Building, Settings, Eye, Zap, AlertTriangle, CheckCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export default function TandemPage() {
  const { toast } = useToast()
  const { data: realtimeData, isConnected: dataConnected } = useRealTimeData()
  const { isConnected: tandemConnected, syncMachineData, loading } = useTandemSync()

  const [facilityId, setFacilityId] = useState(process.env.NEXT_PUBLIC_TANDEM_FACILITY_ID || "")
  const [modelId, setModelId] = useState(process.env.NEXT_PUBLIC_TANDEM_MODEL_ID || "")
  const [autoSync, setAutoSync] = useState(true)

  // Synchronisation automatique des données temps réel
  useState(() => {
    if (autoSync && realtimeData && tandemConnected) {
      syncMachineData(realtimeData).catch(console.error)
    }
  }, [realtimeData, autoSync, tandemConnected])

  const handleManualSync = async () => {
    if (!realtimeData) {
      toast({
        title: "Aucune donnée",
        description: "Aucune donnée temps réel disponible pour la synchronisation.",
        variant: "destructive",
      })
      return
    }

    try {
      await syncMachineData(realtimeData)
      toast({
        title: "Synchronisation réussie",
        description: "Les données ont été synchronisées avec Tandem.",
      })
    } catch (error) {
      toast({
        title: "Erreur de synchronisation",
        description: "Impossible de synchroniser avec Tandem.",
        variant: "destructive",
      })
    }
  }

  const handleTestConnection = async () => {
    try {
      const response = await fetch("/api/tandem/sync")
      const data = await response.json()

      if (response.ok) {
        toast({
          title: "Connexion réussie",
          description: "La connexion avec Tandem fonctionne correctement.",
        })
      } else {
        throw new Error(data.error || "Erreur de connexion")
      }
    } catch (error) {
      toast({
        title: "Erreur de connexion",
        description: error instanceof Error ? error.message : "Impossible de se connecter à Tandem.",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Intégration Autodesk Tandem</h2>
          <p className="text-muted-foreground">Visualisation 3D et synchronisation avec le projet Revit</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={dataConnected ? "default" : "destructive"}>
            {dataConnected ? "🟢 Données" : "🔴 Données"}
          </Badge>
          <Badge variant={tandemConnected ? "default" : "destructive"}>
            {tandemConnected ? "🟢 Tandem" : "🔴 Tandem"}
          </Badge>
        </div>
      </div>

      <Tabs defaultValue="viewer" className="space-y-4">
        <TabsList>
          <TabsTrigger value="viewer">🏗️ Viewer 3D</TabsTrigger>
          <TabsTrigger value="sync">🔄 Synchronisation</TabsTrigger>
          <TabsTrigger value="config">⚙️ Configuration</TabsTrigger>
          <TabsTrigger value="node-red">📡 Node-RED</TabsTrigger>
        </TabsList>

        <TabsContent value="viewer" className="space-y-4">
          <TandemViewer facilityId={facilityId} modelId={modelId} className="w-full" />
        </TabsContent>

        <TabsContent value="sync" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Synchronisation Temps Réel
                </CardTitle>
                <CardDescription>Synchronisation automatique des données machine avec Tandem</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Synchronisation automatique</div>
                    <div className="text-sm text-muted-foreground">
                      Synchronise automatiquement les nouvelles données
                    </div>
                  </div>
                  <Button variant={autoSync ? "default" : "outline"} onClick={() => setAutoSync(!autoSync)}>
                    {autoSync ? "Activée" : "Désactivée"}
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Synchronisation manuelle</div>
                    <div className="text-sm text-muted-foreground">Forcer la synchronisation des données actuelles</div>
                  </div>
                  <Button onClick={handleManualSync} disabled={loading || !realtimeData}>
                    {loading ? "Synchronisation..." : "Synchroniser"}
                  </Button>
                </div>

                {realtimeData && (
                  <div className="p-4 border rounded-lg bg-muted/50">
                    <div className="font-medium mb-2">Données actuelles:</div>
                    <div className="grid gap-1 text-sm">
                      <div>Vibration X: {realtimeData.vibration_x.toFixed(3)} mm/s</div>
                      <div>Vibration Y: {realtimeData.vibration_y.toFixed(3)} mm/s</div>
                      <div>Vibration Z: {realtimeData.vibration_z.toFixed(3)} mm/s</div>
                      <div>État: {realtimeData.etat_machine}</div>
                      <div>Source: {realtimeData.source}</div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Statut Tandem
                </CardTitle>
                <CardDescription>Informations sur la connexion et le modèle</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Connexion Tandem</div>
                    <div className="text-sm text-muted-foreground">Statut de la connexion API</div>
                  </div>
                  <div className="flex items-center gap-2">
                    {tandemConnected ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-5 w-5 text-red-500" />
                    )}
                    <Badge variant={tandemConnected ? "default" : "destructive"}>
                      {tandemConnected ? "Connecté" : "Déconnecté"}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="font-medium">Configuration actuelle:</div>
                  <div className="text-sm space-y-1">
                    <div>
                      <strong>Facility ID:</strong> {facilityId || "Non configuré"}
                    </div>
                    <div>
                      <strong>Model ID:</strong> {modelId || "Non configuré"}
                    </div>
                  </div>
                </div>

                <Button onClick={handleTestConnection} className="w-full">
                  Tester la connexion
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="config" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Configuration Tandem
              </CardTitle>
              <CardDescription>Configuration de la connexion avec Autodesk Tandem</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="facilityId">Facility ID</Label>
                  <Input
                    id="facilityId"
                    value={facilityId}
                    onChange={(e) => setFacilityId(e.target.value)}
                    placeholder="ID de la facility Tandem"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="modelId">Model ID</Label>
                  <Input
                    id="modelId"
                    value={modelId}
                    onChange={(e) => setModelId(e.target.value)}
                    placeholder="ID du modèle Revit"
                  />
                </div>
              </div>

              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Configuration requise:</strong> Assurez-vous d'avoir configuré les variables d'environnement
                  TANDEM_CLIENT_ID, TANDEM_CLIENT_SECRET, TANDEM_FACILITY_ID et TANDEM_MODEL_ID.
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <div className="font-medium">Variables d'environnement requises:</div>
                <div className="text-sm space-y-1 font-mono bg-muted p-3 rounded">
                  <div>TANDEM_CLIENT_ID=your_client_id</div>
                  <div>TANDEM_CLIENT_SECRET=your_client_secret</div>
                  <div>TANDEM_FACILITY_ID=your_facility_id</div>
                  <div>TANDEM_MODEL_ID=your_model_id</div>
                  <div>TANDEM_ENABLED=true</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="node-red" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Configuration Node-RED
              </CardTitle>
              <CardDescription>Instructions pour configurer Node-RED avec le dashboard</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Endpoint Node-RED:</strong> Configurez Node-RED pour envoyer les données vers
                  <code className="ml-1 px-1 py-0.5 bg-muted rounded">POST /api/node-red</code>
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div>
                  <div className="font-medium mb-2">Format des données attendu:</div>
                  <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
                    {`{
  "payload": {
    "vibration_data": {
      "x": 1.23,
      "y": 0.87,
      "z": 2.15
    },
    "machine_state": "en_marche",
    "additional_data": {
      "temperature": 45.2,
      "pressure": 2.1
    }
  },
  "topic": "machine/vibration"
}`}
                  </pre>
                </div>

                <div>
                  <div className="font-medium mb-2">Configuration Node-RED suggérée:</div>
                  <div className="space-y-2 text-sm">
                    <div>
                      1. <strong>MQTT In</strong> - Réception des données ESP32
                    </div>
                    <div>
                      2. <strong>Function</strong> - Transformation des données
                    </div>
                    <div>
                      3. <strong>HTTP Request</strong> - Envoi vers le dashboard
                    </div>
                    <div>
                      4. <strong>Debug</strong> - Monitoring des données
                    </div>
                  </div>
                </div>

                <div>
                  <div className="font-medium mb-2">Exemple de fonction Node-RED:</div>
                  <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
                    {`// Transformation des données ESP32
const vibrationData = {
  x: msg.payload.x || 0,
  y: msg.payload.y || 0,
  z: msg.payload.z || 0
};

// Détermination de l'état machine
const totalVibration = Math.sqrt(
  vibrationData.x**2 + 
  vibrationData.y**2 + 
  vibrationData.z**2
) / 1000;

let machineState = "en_marche";
if (totalVibration < 0.1) {
  machineState = "arret_production";
} else if (totalVibration > 4.0) {
  machineState = "panne";
}

// Format pour le dashboard
msg.payload = {
  vibration_data: vibrationData,
  machine_state: machineState,
  timestamp: new Date().toISOString()
};

msg.topic = "machine/vibration";
return msg;`}
                  </pre>
                </div>

                <div className="p-4 border rounded-lg bg-green-50 dark:bg-green-900/10">
                  <div className="font-medium text-green-800 dark:text-green-200 mb-1">
                    ✅ Avantages de l'intégration Node-RED:
                  </div>
                  <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
                    <li>• Réception MQTT et HTTP unifiée</li>
                    <li>• Transformation des données en temps réel</li>
                    <li>• Routage vers Firebase et Tandem</li>
                    <li>• Interface graphique pour la configuration</li>
                    <li>• Monitoring et debugging intégrés</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
