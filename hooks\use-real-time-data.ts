"use client"

import { useState, useEffect } from "react"
import { database } from "@/lib/firebase"
import { ref, onValue, off } from "firebase/database"

interface RealtimeData {
  timestamp: string
  vibration_x: number
  vibration_y: number
  vibration_z: number
  etat_machine: string
  source: string
}

export function useRealTimeData() {
  const [data, setData] = useState<RealtimeData | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let currentDataRef

    try {
      // Référence vers les données actuelles
      currentDataRef = ref(database, "current_data")

      // Écouter les changements en temps réel
      const unsubscribe = onValue(
        currentDataRef,
        (snapshot) => {
          if (snapshot.exists()) {
            const newData = snapshot.val()
            setData(newData)
            setIsConnected(true)
            setError(null)
          } else {
            setData(null)
            setIsConnected(false)
          }
        },
        (error) => {
          console.error("Erreur Firebase:", error)
          setError(error.message)
          setIsConnected(false)
        },
      )

      // Nettoyage
      return () => {
        if (currentDataRef) {
          off(currentDataRef)
        }
      }
    } catch (err) {
      console.error("Erreur d'initialisation Firebase:", err)
      setError(err instanceof Error ? err.message : "Erreur inconnue")
      setIsConnected(false)
    }
  }, [])

  return { data, isConnected, error }
}
