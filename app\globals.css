@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Animations personnalisées */
@keyframes pulse-glow {
  0%,
  100% {
    opacity: 1;
    box-shadow: 0 0 5px currentColor;
  }
  50% {
    opacity: 0.5;
    box-shadow: 0 0 20px currentColor;
  }
}

.pulse-glow {
  animation: pulse-glow 2s infinite;
}

/* Styles pour les métriques */
.metric-card {
  @apply bg-gradient-to-br from-blue-500 to-purple-600 text-white p-6 rounded-xl shadow-lg;
}

.metric-card-green {
  @apply bg-gradient-to-br from-green-500 to-emerald-600;
}

.metric-card-red {
  @apply bg-gradient-to-br from-red-500 to-pink-600;
}

.metric-card-blue {
  @apply bg-gradient-to-br from-blue-500 to-cyan-600;
}

.metric-card-purple {
  @apply bg-gradient-to-br from-purple-500 to-indigo-600;
}

/* Styles pour les alertes */
.alert-card {
  @apply p-4 rounded-lg border-l-4 backdrop-blur-sm;
}

.alert-danger {
  @apply bg-red-50 border-red-500 text-red-700 dark:bg-red-900/20 dark:text-red-300;
}

.alert-warning {
  @apply bg-yellow-50 border-yellow-500 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-300;
}

.alert-success {
  @apply bg-green-50 border-green-500 text-green-700 dark:bg-green-900/20 dark:text-green-300;
}

.alert-info {
  @apply bg-blue-50 border-blue-500 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300;
}

/* Styles pour les graphiques */
.chart-container {
  @apply bg-white/5 rounded-xl p-4 backdrop-blur-sm border border-white/10;
}
