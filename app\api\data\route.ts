import { type NextRequest, NextResponse } from "next/server"
import { database } from "@/lib/firebase"
import { ref, push, set } from "firebase/database"

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validation des données
    if (!data.x || !data.y || !data.z) {
      return NextResponse.json({ error: "Données manquantes (x, y, z requis)" }, { status: 400 })
    }

    // Traitement des données d'accélération
    const processedData = {
      timestamp: new Date().toISOString(),
      vibration_x: Number.parseFloat(data.x) / 1000, // Conversion en mm/s
      vibration_y: Number.parseFloat(data.y) / 1000,
      vibration_z: Number.parseFloat(data.z) / 1000,
      etat_machine: determineState(data.x, data.y, data.z),
      source: "esp32",
    }

    // Sauvegarde dans Firebase
    const dataRef = ref(database, "machine_data")
    await push(dataRef, processedData)

    // Sauvegarde des données actuelles pour l'accès temps réel
    const currentRef = ref(database, "current_data")
    await set(currentRef, processedData)

    console.log("Données reçues et sauvegardées:", processedData)

    return NextResponse.json({
      success: true,
      message: "Données reçues et traitées",
      data: processedData,
    })
  } catch (error) {
    console.error("Erreur lors du traitement des données:", error)
    return NextResponse.json({ error: "Erreur interne du serveur" }, { status: 500 })
  }
}

export async function GET() {
  try {
    // Endpoint pour vérifier le statut de l'API
    return NextResponse.json({
      status: "active",
      timestamp: new Date().toISOString(),
      message: "API de réception des données ESP32 opérationnelle",
    })
  } catch (error) {
    return NextResponse.json({ error: "Erreur lors de la vérification du statut" }, { status: 500 })
  }
}

// Ajouter un nouvel endpoint pour Node-RED
export async function PUT(request: NextRequest) {
  try {
    const data = await request.json()

    // Validation des données Node-RED
    if (!data.payload) {
      return NextResponse.json({ error: "Payload manquant" }, { status: 400 })
    }

    const payload = data.payload

    // Support pour différents formats de données Node-RED
    let processedData

    if (payload.vibration_data) {
      // Format direct avec données de vibration
      processedData = {
        timestamp: new Date().toISOString(),
        vibration_x: Number.parseFloat(payload.vibration_data.x) || 0,
        vibration_y: Number.parseFloat(payload.vibration_data.y) || 0,
        vibration_z: Number.parseFloat(payload.vibration_data.z) || 0,
        etat_machine:
          payload.machine_state ||
          determineState(payload.vibration_data.x, payload.vibration_data.y, payload.vibration_data.z),
        source: "node-red",
        node_red_topic: data.topic || "unknown",
        additional_data: payload.additional_data || {},
      }
    } else if (payload.x !== undefined && payload.y !== undefined && payload.z !== undefined) {
      // Format ESP32 via Node-RED
      processedData = {
        timestamp: new Date().toISOString(),
        vibration_x: Number.parseFloat(payload.x) / 1000,
        vibration_y: Number.parseFloat(payload.y) / 1000,
        vibration_z: Number.parseFloat(payload.z) / 1000,
        etat_machine: determineState(payload.x, payload.y, payload.z),
        source: "node-red-esp32",
        node_red_topic: data.topic || "esp32/vibration",
      }
    } else {
      return NextResponse.json({ error: "Format de données non reconnu" }, { status: 400 })
    }

    // Sauvegarde dans Firebase
    const dataRef = ref(database, "machine_data")
    await push(dataRef, processedData)

    // Sauvegarde des données actuelles
    const currentRef = ref(database, "current_data")
    await set(currentRef, processedData)

    // Envoi vers Autodesk Tandem si configuré
    if (process.env.TANDEM_ENABLED === "true") {
      await sendToTandem(processedData)
    }

    console.log("Données Node-RED reçues et traitées:", processedData)

    return NextResponse.json({
      success: true,
      message: "Données Node-RED reçues et traitées",
      data: processedData,
      tandem_sync: process.env.TANDEM_ENABLED === "true",
    })
  } catch (error) {
    console.error("Erreur lors du traitement des données Node-RED:", error)
    return NextResponse.json({ error: "Erreur interne du serveur" }, { status: 500 })
  }
}

// Fonction pour déterminer l'état de la machine basé sur les vibrations
function determineState(x: number, y: number, z: number): string {
  const totalVibration = Math.sqrt(x * x + y * y + z * z) / 1000 // Conversion en mm/s

  if (totalVibration < 0.1) {
    return "arret_production" // Machine arrêtée
  } else if (totalVibration > 4.0) {
    return "panne" // Vibrations anormalement élevées
  } else if (totalVibration > 2.5) {
    return "probleme_qualite" // Vibrations élevées mais pas critiques
  } else {
    return "en_marche" // Fonctionnement normal
  }
}

// Fonction pour envoyer les données vers Autodesk Tandem
async function sendToTandem(data: any) {
  try {
    // Appel vers l'API Tandem
    await fetch("/api/tandem/sync", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(data),
    })
  } catch (error) {
    console.error("Erreur lors de l'envoi vers Tandem:", error)
  }
}
