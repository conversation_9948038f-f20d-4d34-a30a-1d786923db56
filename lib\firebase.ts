import { initializeApp, getApps } from "firebase/app"
import { getDatabase } from "firebase/database"

const firebaseConfig = {
  apiKey: "AIzaSyCSK3HCYAdWZfwrhG3n8_hC5e2c83L9XLg",
  databaseURL: "https://esp32data-ec601-default-rtdb.europe-west1.firebasedatabase.app/",
  projectId: "esp32data-ec601",
}

// Initialiser Firebase
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0]
const database = getDatabase(app)

export { database }
