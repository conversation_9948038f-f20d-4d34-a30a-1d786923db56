[{"id": "mqtt-esp32", "type": "mqtt in", "z": "flow1", "name": "ESP32 Data", "topic": "esp32/vibration", "qos": "0", "datatype": "json", "broker": "mqtt-broker", "x": 100, "y": 100, "wires": [["transform-data"]]}, {"id": "transform-data", "type": "function", "z": "flow1", "name": "Transform Data", "func": "// Transformation des données ESP32\nconst vibrationData = {\n  x: msg.payload.x || 0,\n  y: msg.payload.y || 0,\n  z: msg.payload.z || 0\n};\n\n// Détermination de l'état machine\nconst totalVibration = Math.sqrt(\n  vibrationData.x**2 + \n  vibrationData.y**2 + \n  vibrationData.z**2\n) / 1000;\n\nlet machineState = \"en_marche\";\nif (totalVibration < 0.1) {\n  machineState = \"arret_production\";\n} else if (totalVibration > 4.0) {\n  machineState = \"panne\";\n} else if (totalVibration > 2.5) {\n  machineState = \"probleme_qualite\";\n}\n\n// Format pour le dashboard\nmsg.payload = {\n  vibration_data: vibrationData,\n  machine_state: machineState,\n  timestamp: new Date().toISOString(),\n  additional_data: {\n    total_vibration: totalVibration,\n    source: \"node-red-esp32\"\n  }\n};\n\nmsg.topic = \"machine/vibration\";\nreturn msg;", "x": 300, "y": 100, "wires": [["send-to-dashboard", "debug-output"]]}, {"id": "send-to-dashboard", "type": "http request", "z": "flow1", "name": "Send to Dashboard", "method": "POST", "ret": "obj", "paytoqs": "ignore", "url": "http://localhost:3000/api/node-red", "tls": "", "persist": false, "proxy": "", "authType": "", "x": 500, "y": 100, "wires": [["response-debug"]]}, {"id": "debug-output", "type": "debug", "z": "flow1", "name": "Data Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "x": 500, "y": 160, "wires": []}, {"id": "response-debug", "type": "debug", "z": "flow1", "name": "Response Debug", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "x": 720, "y": 100, "wires": []}, {"id": "mqtt-broker", "type": "mqtt-broker", "name": "Local MQTT", "broker": "localhost", "port": "1883", "clientid": "", "usetls": false, "compatmode": false, "keepalive": "60", "cleansession": true, "birthTopic": "", "birthQos": "0", "birthPayload": "", "closeTopic": "", "closeQos": "0", "closePayload": "", "willTopic": "", "willQos": "0", "willPayload": ""}]