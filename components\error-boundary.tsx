"use client"

import React from "react"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Al<PERSON><PERSON><PERSON>gle, Refresh<PERSON><PERSON>, Settings } from "lucide-react"

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("ErrorBoundary caught an error:", error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <Card className="w-full max-w-2xl mx-auto mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertTriangle className="h-5 w-5" />
              Une erreur s'est produite
            </CardTitle>
            <CardDescription>
              L'application a rencontré une erreur inattendue
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Erreur</AlertTitle>
              <AlertDescription>
                {this.state.error?.message || "Erreur inconnue"}
              </AlertDescription>
            </Alert>
            <div className="flex gap-2">
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Recharger la page
              </Button>
              <Button
                onClick={() => this.setState({ hasError: false })}
                variant="outline"
              >
                Réessayer
              </Button>
            </div>
          </CardContent>
        </Card>
      )
    }

    return this.props.children
  }
}

interface ConnectionStatusProps {
  isConnected: boolean
  error?: string | null
  service: string
  onRetry?: () => void
}

export function ConnectionStatus({ isConnected, error, service, onRetry }: ConnectionStatusProps) {
  if (isConnected) {
    return null
  }

  return (
    <Alert variant={error ? "destructive" : "default"} className="mb-4">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>Problème de connexion - {service}</AlertTitle>
      <AlertDescription className="flex items-center justify-between">
        <span>{error || `Impossible de se connecter à ${service}`}</span>
        {onRetry && (
          <Button onClick={onRetry} variant="outline" size="sm" className="ml-2">
            <RefreshCw className="h-4 w-4 mr-1" />
            Réessayer
          </Button>
        )}
      </AlertDescription>
    </Alert>
  )
}

interface FirebaseErrorProps {
  error: string
}

export function FirebaseError({ error }: FirebaseErrorProps) {
  const getErrorMessage = (error: string) => {
    if (error.includes("permission_denied")) {
      return {
        title: "Erreur de permissions Firebase",
        message: "L'accès aux données Firebase est refusé. Vérifiez les règles de sécurité de votre base de données.",
        suggestions: [
          "Vérifiez que les règles de sécurité Firebase permettent l'accès en lecture/écriture",
          "Assurez-vous que la configuration Firebase est correcte",
          "Vérifiez que l'API Key Firebase est valide"
        ]
      }
    }
    
    if (error.includes("network")) {
      return {
        title: "Erreur de réseau",
        message: "Impossible de se connecter à Firebase. Vérifiez votre connexion internet.",
        suggestions: [
          "Vérifiez votre connexion internet",
          "Vérifiez que l'URL de la base de données Firebase est correcte"
        ]
      }
    }

    return {
      title: "Erreur Firebase",
      message: error,
      suggestions: [
        "Vérifiez la configuration Firebase dans les variables d'environnement",
        "Consultez la console Firebase pour plus de détails"
      ]
    }
  }

  const errorInfo = getErrorMessage(error)

  return (
    <Alert variant="destructive" className="mb-4">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>{errorInfo.title}</AlertTitle>
      <AlertDescription>
        <div className="space-y-2">
          <p>{errorInfo.message}</p>
          <div className="text-sm">
            <p className="font-medium">Suggestions:</p>
            <ul className="list-disc list-inside space-y-1">
              {errorInfo.suggestions.map((suggestion, index) => (
                <li key={index}>{suggestion}</li>
              ))}
            </ul>
          </div>
        </div>
      </AlertDescription>
    </Alert>
  )
}
