"use client"

import React from "react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertTriangle } from "lucide-react"

interface SafeRenderProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  componentName?: string
}

interface SafeRenderState {
  hasError: boolean
  error?: Error
}

export class SafeRender extends React.Component<SafeRenderProps, SafeRenderState> {
  constructor(props: SafeRenderProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): SafeRenderState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error(`SafeRender caught error in ${this.props.componentName || 'component'}:`, error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <Alert variant="destructive" className="my-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Erreur dans le composant {this.props.componentName || 'inconnu'}: {this.state.error?.message || 'Erreur inconnue'}
          </AlertDescription>
        </Alert>
      )
    }

    return this.props.children
  }
}

// Hook pour valider les données avant le rendu
export function useSafeData<T>(data: T, validator?: (data: T) => boolean): T | null {
  try {
    if (validator && !validator(data)) {
      console.warn('Data validation failed:', data)
      return null
    }
    return data
  } catch (error) {
    console.error('Error in useSafeData:', error)
    return null
  }
}

// Fonction utilitaire pour s'assurer qu'une valeur est un tableau
export function ensureArray<T>(value: any): T[] {
  if (Array.isArray(value)) {
    return value
  }
  if (value === null || value === undefined) {
    return []
  }
  console.warn('Expected array but got:', typeof value, value)
  return []
}

// Fonction utilitaire pour s'assurer qu'une valeur est une chaîne
export function ensureString(value: any): string {
  if (typeof value === 'string') {
    return value
  }
  if (value === null || value === undefined) {
    return ''
  }
  return String(value)
}

// Fonction utilitaire pour s'assurer qu'une valeur est un nombre
export function ensureNumber(value: any): number {
  if (typeof value === 'number' && !isNaN(value)) {
    return value
  }
  const parsed = parseFloat(value)
  if (!isNaN(parsed)) {
    return parsed
  }
  return 0
}
