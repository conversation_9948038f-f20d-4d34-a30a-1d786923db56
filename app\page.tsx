"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { RefreshCw, Activity, AlertTriangle, CheckCircle, XCircle } from "lucide-react"
import { VibrationChart } from "@/components/charts/vibration-chart"
import { MachineStateTimeline } from "@/components/charts/machine-state-timeline"
import { MetricCard } from "@/components/metric-card"
import { useRealTimeData } from "@/hooks/use-real-time-data"
import { useMachineData } from "@/hooks/use-machine-data"
import { useTandemSync } from "@/hooks/use-tandem-sync"
import { ConnectionStatus, FirebaseError } from "@/components/error-boundary"
import { DebugData } from "@/components/debug-data"

export default function Dashboard() {
  const [timeRange, setTimeRange] = useState("6")
  const [autoRefresh, setAutoRefresh] = useState(false)
  const [realTime, setRealTime] = useState(true)

  // Hooks pour les données en temps réel
  const { data: realtimeData, isConnected, error: realtimeError } = useRealTimeData()
  const { data: machineData, loading, refetch, error: machineDataError } = useMachineData(Number.parseInt(timeRange))

  const { isConnected: tandemConnected, syncMachineData, error: tandemError } = useTandemSync()

  // Synchronisation automatique avec Tandem quand de nouvelles données arrivent
  useEffect(() => {
    if (realtimeData && tandemConnected) {
      syncMachineData(realtimeData).catch(console.error)
    }
  }, [realtimeData, tandemConnected, syncMachineData])

  // Auto-refresh
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        refetch()
      }, 30000) // 30 secondes
      return () => clearInterval(interval)
    }
  }, [autoRefresh, refetch])

  const currentState = realtimeData || machineData?.[machineData.length - 1]

  // Debug logging
  useEffect(() => {
    console.log("Debug - realtimeData:", realtimeData)
    console.log("Debug - machineData:", machineData)
    console.log("Debug - currentState:", currentState)
  }, [realtimeData, machineData, currentState])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "en_marche":
        return "text-green-500"
      case "panne":
        return "text-red-500"
      case "arret_production":
        return "text-blue-500"
      case "probleme_qualite":
        return "text-yellow-500"
      default:
        return "text-gray-500"
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "en_marche":
        return "En Marche"
      case "panne":
        return "Panne"
      case "arret_production":
        return "Arrêt Production"
      case "probleme_qualite":
        return "Problème Qualité"
      default:
        return "Inconnu"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "en_marche":
        return <CheckCircle className="h-5 w-5" />
      case "panne":
        return <XCircle className="h-5 w-5" />
      case "arret_production":
        return <Activity className="h-5 w-5" />
      case "probleme_qualite":
        return <AlertTriangle className="h-5 w-5" />
      default:
        return <Activity className="h-5 w-5" />
    }
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      {/* En-tête */}
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Dashboard Maintenance Prédictive
          </h2>
          <p className="text-muted-foreground">Surveillance en temps réel de la machine de coupe industrielle</p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={isConnected ? "default" : "destructive"} className="pulse-glow">
            {isConnected ? "🟢 Connecté" : "🔴 Déconnecté"}
          </Badge>
          <Badge variant={tandemConnected ? "default" : "secondary"} className="pulse-glow">
            {tandemConnected ? "🏗️ Tandem Sync" : "🏗️ Tandem Off"}
          </Badge>
        </div>
      </div>

      {/* Debug Data */}
      <div className="grid gap-4 md:grid-cols-3">
        <DebugData title="Real-time Data" data={realtimeData} />
        <DebugData title="Machine Data" data={machineData} />
        <DebugData title="Current State" data={currentState} />
      </div>

      {/* Alertes d'erreur */}
      {realtimeError && <FirebaseError error={realtimeError} />}
      {machineDataError && (
        <ConnectionStatus
          isConnected={false}
          error={machineDataError}
          service="Données Machine"
          onRetry={refetch}
        />
      )}
      {tandemError && (
        <ConnectionStatus
          isConnected={tandemConnected}
          error={tandemError}
          service="Tandem"
        />
      )}

      {/* Contrôles */}
      <div className="flex flex-wrap items-center gap-4 p-4 bg-card rounded-lg border">
        <div className="flex items-center space-x-2">
          <Label htmlFor="time-range">Période:</Label>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">1h</SelectItem>
              <SelectItem value="3">3h</SelectItem>
              <SelectItem value="6">6h</SelectItem>
              <SelectItem value="12">12h</SelectItem>
              <SelectItem value="24">24h</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <Switch id="auto-refresh" checked={autoRefresh} onCheckedChange={setAutoRefresh} />
          <Label htmlFor="auto-refresh">Auto-refresh</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Switch id="real-time" checked={realTime} onCheckedChange={setRealTime} />
          <Label htmlFor="real-time">Temps réel</Label>
        </div>

        <Button onClick={() => refetch()} disabled={loading}>
          <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
          Actualiser
        </Button>
      </div>

      {/* Métriques principales */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="État Machine"
          value={currentState && typeof currentState.etat_machine === 'string' ? getStatusLabel(currentState.etat_machine) : "N/A"}
          icon={currentState && typeof currentState.etat_machine === 'string' ? getStatusIcon(currentState.etat_machine) : <Activity />}
          className={currentState && typeof currentState.etat_machine === 'string' ? getStatusColor(currentState.etat_machine) : ""}
          trend={currentState?.etat_machine === "en_marche" ? "up" : "down"}
        />

        <MetricCard
          title="Vibration X"
          value={currentState && typeof currentState.vibration_x === 'number' ? `${currentState.vibration_x.toFixed(2)} mm/s` : "N/A"}
          icon={<Activity />}
          trend={currentState?.vibration_x > 2 ? "down" : "up"}
          delta={
            machineData && currentState && typeof currentState.vibration_x === 'number'
              ? `Δ ${(currentState.vibration_x - machineData.reduce((acc, d) => acc + d.vibration_x, 0) / machineData.length).toFixed(2)}`
              : undefined
          }
        />

        <MetricCard
          title="Vibration Y"
          value={currentState && typeof currentState.vibration_y === 'number' ? `${currentState.vibration_y.toFixed(2)} mm/s` : "N/A"}
          icon={<Activity />}
          trend={currentState?.vibration_y > 2 ? "down" : "up"}
          delta={
            machineData && currentState && typeof currentState.vibration_y === 'number'
              ? `Δ ${(currentState.vibration_y - machineData.reduce((acc, d) => acc + d.vibration_y, 0) / machineData.length).toFixed(2)}`
              : undefined
          }
        />

        <MetricCard
          title="Vibration Z"
          value={currentState && typeof currentState.vibration_z === 'number' ? `${currentState.vibration_z.toFixed(2)} mm/s` : "N/A"}
          icon={<Activity />}
          trend={currentState?.vibration_z > 2 ? "down" : "up"}
          delta={
            machineData && currentState && typeof currentState.vibration_z === 'number'
              ? `Δ ${(currentState.vibration_z - machineData.reduce((acc, d) => acc + d.vibration_z, 0) / machineData.length).toFixed(2)}`
              : undefined
          }
        />
      </div>

      {/* Timeline des états */}
      <Card>
        <CardHeader>
          <CardTitle>Timeline des États de la Machine</CardTitle>
          <CardDescription>Évolution des états de la machine au cours du temps</CardDescription>
        </CardHeader>
        <CardContent>
          <MachineStateTimeline data={Array.isArray(machineData) ? machineData : []} />
        </CardContent>
      </Card>

      {/* Graphiques des vibrations */}
      <div className="grid gap-4 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle>Évolution des Vibrations en Temps Réel</CardTitle>
            <CardDescription>Surveillance continue des vibrations sur les trois axes</CardDescription>
          </CardHeader>
          <CardContent>
            <VibrationChart data={Array.isArray(machineData) ? machineData : []} realTimeData={realtimeData} />
          </CardContent>
        </Card>
      </div>

      {/* Graphiques individuels */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Vibration X</CardTitle>
          </CardHeader>
          <CardContent>
            <VibrationChart data={Array.isArray(machineData) ? machineData : []} realTimeData={realtimeData} axis="x" height={300} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Vibration Y</CardTitle>
          </CardHeader>
          <CardContent>
            <VibrationChart data={Array.isArray(machineData) ? machineData : []} realTimeData={realtimeData} axis="y" height={300} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Vibration Z</CardTitle>
          </CardHeader>
          <CardContent>
            <VibrationChart data={Array.isArray(machineData) ? machineData : []} realTimeData={realtimeData} axis="z" height={300} />
          </CardContent>
        </Card>
      </div>

      {/* Indicateurs de performance */}
      <Card>
        <CardHeader>
          <CardTitle>Indicateurs de Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-5">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">85.2%</div>
              <div className="text-sm text-muted-foreground">Efficacité</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">92.1%</div>
              <div className="text-sm text-muted-foreground">Disponibilité</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">96.8%</div>
              <div className="text-sm text-muted-foreground">Qualité</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">76.4%</div>
              <div className="text-sm text-muted-foreground">OEE</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">🟢 88.5%</div>
              <div className="text-sm text-muted-foreground">Santé Globale</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
