import { NextResponse } from "next/server"

// Simulateur de données de capteur
export async function POST() {
  try {
    // Générer des données de vibration aléatoires mais réalistes
    const generateVibrationData = () => {
      const baseVibration = 0.5 + Math.random() * 1.5 // 0.5 à 2.0 mm/s
      const noise = (Math.random() - 0.5) * 0.2 // Bruit ±0.1

      return {
        x: Math.max(0, baseVibration + noise + Math.sin(Date.now() / 1000) * 0.3),
        y: Math.max(0, baseVibration + noise + Math.cos(Date.now() / 1000) * 0.3),
        z: Math.max(0, baseVibration + noise + Math.sin(Date.now() / 2000) * 0.2),
      }
    }

    // Simuler différents états de machine
    const states = ["en_marche", "panne", "arret_production", "probleme_qualite"]
    const randomState = states[Math.floor(Math.random() * states.length)]

    let vibrationData = generateVibrationData()

    // Ajuster les vibrations selon l'état
    switch (randomState) {
      case "panne":
        vibrationData = {
          x: 4.0 + Math.random() * 2.0, // Vibrations très élevées
          y: 4.0 + Math.random() * 2.0,
          z: 4.0 + Math.random() * 2.0,
        }
        break
      case "arret_production":
        vibrationData = {
          x: 0.01 + Math.random() * 0.05, // Vibrations très faibles
          y: 0.01 + Math.random() * 0.05,
          z: 0.01 + Math.random() * 0.05,
        }
        break
      case "probleme_qualite":
        vibrationData = {
          x: 2.5 + Math.random() * 1.0, // Vibrations moyennes-élevées
          y: 2.5 + Math.random() * 1.0,
          z: 2.5 + Math.random() * 1.0,
        }
        break
    }

    // Convertir en format ESP32 (multiplier par 1000)
    const esp32Data = {
      x: Math.round(vibrationData.x * 1000),
      y: Math.round(vibrationData.y * 1000),
      z: Math.round(vibrationData.z * 1000),
    }

    // Envoyer vers l'API principale
    const response = await fetch(`${process.env.NEXTAUTH_URL || "http://localhost:3000"}/api/data`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(esp32Data),
    })

    const result = await response.json()

    return NextResponse.json({
      success: true,
      message: "Données simulées envoyées avec succès",
      simulatedData: {
        original: esp32Data,
        processed: result.data,
        state: randomState,
      },
      result,
    })
  } catch (error) {
    console.error("Erreur simulation:", error)
    return NextResponse.json(
      {
        error: "Erreur lors de la simulation",
        details: error instanceof Error ? error.message : "Erreur inconnue",
      },
      { status: 500 },
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: "Simulateur de données ESP32",
    usage: "POST /api/test/simulate pour générer des données aléatoires",
    endpoints: {
      simulate: "POST /api/test/simulate - Génère et envoie des données aléatoires",
      "simulate-batch": "POST /api/test/simulate-batch - Génère plusieurs points de données",
      "simulate-scenario": "POST /api/test/simulate-scenario - Simule un scénario spécifique",
    },
  })
}
