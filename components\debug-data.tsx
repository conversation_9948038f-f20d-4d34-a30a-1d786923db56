"use client"

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"

interface DebugDataProps {
  title: string
  data: any
}

export function DebugData({ title, data }: DebugDataProps) {
  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle className="text-sm">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-40">
          {JSON.stringify(data, null, 2)}
        </pre>
        <div className="mt-2 text-xs text-muted-foreground">
          Type: {typeof data} | Array: {Array.isArray(data) ? 'Yes' : 'No'}
        </div>
      </CardContent>
    </Card>
  )
}
