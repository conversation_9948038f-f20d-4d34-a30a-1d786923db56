import { NextResponse } from "next/server"

// Simulateur pour tester l'endpoint Node-RED
export async function POST() {
  try {
    // Générer des données au format Node-RED
    const nodeRedData = {
      payload: {
        vibration_data: {
          x: 0.5 + Math.random() * 2.0, // 0.5 à 2.5 mm/s
          y: 0.5 + Math.random() * 2.0,
          z: 0.5 + Math.random() * 2.0,
        },
        machine_state: ["en_marche", "probleme_qualite"][Math.floor(Math.random() * 2)],
        additional_data: {
          temperature: 20 + Math.random() * 30, // 20-50°C
          pressure: 1.0 + Math.random() * 2.0, // 1-3 bar
          humidity: 30 + Math.random() * 40, // 30-70%
          source: "simulator",
        },
      },
      topic: "machine/vibration/test",
      timestamp: new Date().toISOString(),
    }

    // Envoyer vers l'endpoint Node-RED
    const response = await fetch(`${process.env.NEXTAUTH_URL || "http://localhost:3000"}/api/node-red`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(nodeRedData),
    })

    const result = await response.json()

    return NextResponse.json({
      success: true,
      message: "Données Node-RED simulées envoyées",
      simulatedData: nodeRedData,
      result,
    })
  } catch (error) {
    console.error("Erreur simulation Node-RED:", error)
    return NextResponse.json(
      {
        error: "Erreur lors de la simulation Node-RED",
        details: error instanceof Error ? error.message : "Erreur inconnue",
      },
      { status: 500 },
    )
  }
}
