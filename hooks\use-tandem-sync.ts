"use client"

import { useState, useEffect, useCallback } from "react"

interface TandemElement {
  elementId: string
  properties: Record<string, any>
  status: string
  alerts: any[]
}

export function useTandemSync() {
  const [elements, setElements] = useState<TandemElement[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [lastSync, setLastSync] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const syncData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Récupérer les éléments depuis l'API Tandem
      const response = await fetch("/api/tandem/sync")

      if (!response.ok) {
        throw new Error(`Erreur API: ${response.status}`)
      }

      const data = await response.json()

      if (data.elements) {
        setElements(data.elements)
        setIsConnected(true)
        setLastSync(new Date().toISOString())
      }
    } catch (err) {
      console.error("Erreur de synchronisation Tandem:", err)
      setError(err instanceof Error ? err.message : "Erreur inconnue")
      setIsConnected(false)
    } finally {
      setLoading(false)
    }
  }, [])

  const syncMachineData = useCallback(
    async (machineData: any) => {
      try {
        const response = await fetch("/api/tandem/sync", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(machineData),
        })

        if (!response.ok) {
          throw new Error(`Erreur de synchronisation: ${response.status}`)
        }

        const result = await response.json()

        if (result.success) {
          setLastSync(new Date().toISOString())
          setIsConnected(true)
          // Rafraîchir les éléments après synchronisation
          await syncData()
        }

        return result
      } catch (err) {
        console.error("Erreur lors de l'envoi vers Tandem:", err)
        setError(err instanceof Error ? err.message : "Erreur d'envoi")
        throw err
      }
    },
    [syncData],
  )

  // Synchronisation automatique au démarrage
  useEffect(() => {
    syncData()
  }, [syncData])

  // Synchronisation périodique
  useEffect(() => {
    const interval = setInterval(() => {
      if (isConnected) {
        syncData()
      }
    }, 30000) // Toutes les 30 secondes

    return () => clearInterval(interval)
  }, [isConnected, syncData])

  return {
    elements,
    isConnected,
    lastSync,
    loading,
    error,
    syncData,
    syncMachineData,
  }
}
