import { type NextRequest, NextResponse } from "next/server"
import { database } from "@/lib/firebase"
import { ref, query, orderByChild, limitToLast, get } from "firebase/database"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const hours = Number.parseInt(searchParams.get("hours") || "6")
    const limit = Number.parseInt(searchParams.get("limit") || "1000")

    // Récupération des données depuis Firebase
    const dataRef = ref(database, "machine_data")
    const dataQuery = query(dataRef, orderByChild("timestamp"), limitToLast(limit))

    const snapshot = await get(dataQuery)

    if (!snapshot.exists()) {
      return NextResponse.json({ data: [] })
    }

    const allData = Object.values(snapshot.val()) as any[]

    // Filtrage par période
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000)
    const filteredData = allData.filter((item) => new Date(item.timestamp) >= cutoffTime)

    // Tri par timestamp
    const sortedData = filteredData.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())

    return NextResponse.json({
      data: sortedData,
      count: sortedData.length,
      period: `${hours}h`,
    })
  } catch (error) {
    console.error("Erreur lors de la récupération des données Firebase:", error)
    return NextResponse.json({ error: "Erreur lors de la récupération des données" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json()

    if (action === "current") {
      // Récupération des données actuelles
      const currentRef = ref(database, "current_data")
      const snapshot = await get(currentRef)

      if (snapshot.exists()) {
        return NextResponse.json({ data: snapshot.val() })
      } else {
        return NextResponse.json({ data: null })
      }
    }

    return NextResponse.json({ error: "Action non reconnue" }, { status: 400 })
  } catch (error) {
    console.error("Erreur lors de l'action Firebase:", error)
    return NextResponse.json({ error: "Erreur lors de l'exécution de l'action" }, { status: 500 })
  }
}
