"use client"

import { Home, Activity, AlertTriangle, BarChart3, Cog, Building, TestTube } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
} from "@/components/ui/sidebar"
import { Badge } from "@/components/ui/badge"

const items = [
  {
    title: "Suivi Instantané",
    url: "/",
    icon: Home,
    badge: null,
  },
  {
    title: "Saisie Causes d'Arrêt",
    url: "/arrets",
    icon: AlertTriangle,
    badge: null,
  },
  {
    title: "Détection Automatique",
    url: "/detection",
    icon: Activity,
    badge: "3",
  },
  {
    title: "Historique Machine",
    url: "/historique",
    icon: BarChart3,
    badge: null,
  },
  {
    title: "Intégration Tandem",
    url: "/tandem",
    icon: Building,
    badge: "NEW",
  },
  {
    title: "Centre de Test",
    url: "/test",
    icon: TestTube,
    badge: "🧪",
  },
  {
    title: "Configuration",
    url: "/configuration",
    icon: Cog,
    badge: null,
  },
]

export function AppSidebar() {
  const pathname = usePathname()

  return (
    <Sidebar>
      <SidebarHeader className="border-b border-sidebar-border">
        <div className="flex items-center gap-2 px-4 py-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            <Activity className="h-4 w-4" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold">Altutex</span>
            <span className="truncate text-xs text-muted-foreground">Machine de Coupe</span>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Dashboard</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {items.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild isActive={pathname === item.url}>
                    <Link href={item.url} className="flex items-center gap-2">
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                      {item.badge && (
                        <Badge variant="secondary" className="ml-auto">
                          {item.badge}
                        </Badge>
                      )}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t border-sidebar-border">
        <div className="p-4">
          <div className="flex items-center gap-2 text-sm">
            <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse" />
            <span className="text-muted-foreground">Système opérationnel</span>
          </div>
          <div className="mt-2 text-xs text-muted-foreground">v1.0.0 | Next.js Dashboard</div>
        </div>
      </SidebarFooter>
    </Sidebar>
  )
}
