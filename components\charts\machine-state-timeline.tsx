"use client"

import { useMemo } from "react"

interface MachineData {
  timestamp: string
  etat_machine: string
  vibration_x: number
  vibration_y: number
  vibration_z: number
}

interface MachineStateTimelineProps {
  data: MachineData[]
}

// Constantes pour éviter tout problème de rendu
const STATE_COLORS: Record<string, string> = {
  en_marche: "#22c55e",
  panne: "#ef4444",
  arret_production: "#3b82f6",
  probleme_qualite: "#f59e0b",
}

const STATE_LABELS: Record<string, string> = {
  en_marche: "En Marche",
  panne: "Panne",
  arret_production: "Arrêt Production",
  probleme_qualite: "Problème Qualité",
}

// Fonction utilitaire pour obtenir la couleur de manière sûre
const getStateColor = (state: string): string => {
  return STATE_COLORS[state] || "#6b7280"
}

// Fonction utilitaire pour obtenir le label de manière sûre
const getStateLabel = (state: string): string => {
  return STATE_LABELS[state] || state
}

export function MachineStateTimeline({ data }: MachineStateTimelineProps) {
  // Validation stricte des données d'entrée
  const validData = useMemo(() => {
    if (!data || !Array.isArray(data)) {
      console.warn("MachineStateTimeline: data is not an array", data)
      return []
    }
    return data.filter(item =>
      item &&
      typeof item === 'object' &&
      typeof item.timestamp === 'string' &&
      typeof item.etat_machine === 'string'
    )
  }, [data])

  const segments = useMemo(() => {
    if (!validData.length) return []

    const result = []
    let currentState = validData[0].etat_machine
    let startTime = new Date(validData[0].timestamp)
    let startIndex = 0

    for (let i = 1; i < validData.length; i++) {
      if (validData[i].etat_machine !== currentState) {
        const endTime = new Date(validData[i].timestamp)
        const duration = (endTime.getTime() - startTime.getTime()) / (1000 * 60) // en minutes

        result.push({
          state: currentState,
          startTime,
          endTime,
          duration,
          startIndex,
          endIndex: i - 1,
        })

        currentState = validData[i].etat_machine
        startTime = endTime
        startIndex = i
      }
    }

    // Ajouter le dernier segment
    const endTime = new Date(validData[validData.length - 1].timestamp)
    const duration = (endTime.getTime() - startTime.getTime()) / (1000 * 60)

    result.push({
      state: currentState,
      startTime,
      endTime,
      duration,
      startIndex,
      endIndex: validData.length - 1,
    })

    return result
  }, [validData])

  const totalDuration = useMemo(() => {
    if (!validData.length) return 0
    const start = new Date(validData[0].timestamp)
    const end = new Date(validData[validData.length - 1].timestamp)
    return (end.getTime() - start.getTime()) / (1000 * 60) // en minutes
  }, [validData])

  if (!validData.length) {
    return <div className="flex items-center justify-center h-32 text-muted-foreground">Aucune donnée disponible</div>
  }

  return (
    <div className="space-y-4">
      {/* Timeline visuelle */}
      <div className="relative h-16 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
        {segments.map((segment, index) => {
          const widthPercent = totalDuration > 0 ? (segment.duration / totalDuration) * 100 : 0
          const color = getStateColor(segment.state)
          const label = getStateLabel(segment.state)

          return (
            <div
              key={`segment-${index}-${segment.state}`}
              className="absolute top-0 h-full flex items-center justify-center text-white text-xs font-medium transition-all hover:opacity-80 cursor-pointer"
              style={{
                left: `${segments.slice(0, index).reduce((acc, s) => acc + (s.duration / totalDuration) * 100, 0)}%`,
                width: `${widthPercent}%`,
                backgroundColor: color,
                minWidth: widthPercent > 5 ? "auto" : "2px",
              }}
              title={`${label} - ${segment.duration.toFixed(1)} min`}
            >
              {widthPercent > 10 && (
                <span className="truncate px-1">{label}</span>
              )}
            </div>
          )
        })}
      </div>

      {/* Légende */}
      <div className="flex flex-wrap gap-4 text-sm">
        {Object.keys(STATE_COLORS).map((state) => (
          <div key={`legend-${state}`} className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: getStateColor(state) }} />
            <span className="text-muted-foreground">{getStateLabel(state)}</span>
          </div>
        ))}
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        {(() => {
          // Calculer les statistiques de manière sûre
          const stats: Record<string, { duration: number; count: number }> = {}

          segments.forEach((segment) => {
            const state = segment.state
            if (!stats[state]) {
              stats[state] = { duration: 0, count: 0 }
            }
            stats[state].duration += segment.duration
            stats[state].count += 1
          })

          // Convertir en tableau pour le rendu
          return Object.keys(stats).map((state) => {
            const statData = stats[state]
            const percentage = totalDuration > 0 ? ((statData.duration / totalDuration) * 100).toFixed(1) : '0'

            return (
              <div key={`stat-${state}`} className="text-center p-3 bg-muted rounded-lg">
                <div className="font-medium text-lg">{percentage}%</div>
                <div className="text-muted-foreground text-xs">{getStateLabel(state)}</div>
                <div className="text-muted-foreground text-xs">{statData.duration.toFixed(0)} min</div>
              </div>
            )
          })
        })()}
      </div>
    </div>
  )
}
