"use client"

import { useMemo } from "react"

interface MachineData {
  timestamp: string
  etat_machine: string
  vibration_x: number
  vibration_y: number
  vibration_z: number
}

interface MachineStateTimelineProps {
  data: MachineData[]
}

const stateColors = {
  en_marche: "#22c55e", // Vert
  panne: "#ef4444", // Rouge
  arret_production: "#3b82f6", // Bleu
  probleme_qualite: "#f59e0b", // Orange
}

const stateLabels = {
  en_marche: "En Marche",
  panne: "Panne",
  arret_production: "Arrêt Production",
  probleme_qualite: "Problème Qualité",
}

export function MachineStateTimeline({ data }: MachineStateTimelineProps) {
  const segments = useMemo(() => {
    if (!data.length) return []

    const result = []
    let currentState = data[0].etat_machine
    let startTime = new Date(data[0].timestamp)
    let startIndex = 0

    for (let i = 1; i < data.length; i++) {
      if (data[i].etat_machine !== currentState) {
        const endTime = new Date(data[i].timestamp)
        const duration = (endTime.getTime() - startTime.getTime()) / (1000 * 60) // en minutes

        result.push({
          state: currentState,
          startTime,
          endTime,
          duration,
          startIndex,
          endIndex: i - 1,
        })

        currentState = data[i].etat_machine
        startTime = endTime
        startIndex = i
      }
    }

    // Ajouter le dernier segment
    const endTime = new Date(data[data.length - 1].timestamp)
    const duration = (endTime.getTime() - startTime.getTime()) / (1000 * 60)

    result.push({
      state: currentState,
      startTime,
      endTime,
      duration,
      startIndex,
      endIndex: data.length - 1,
    })

    return result
  }, [data])

  const totalDuration = useMemo(() => {
    if (!data.length) return 0
    const start = new Date(data[0].timestamp)
    const end = new Date(data[data.length - 1].timestamp)
    return (end.getTime() - start.getTime()) / (1000 * 60) // en minutes
  }, [data])

  if (!data.length) {
    return <div className="flex items-center justify-center h-32 text-muted-foreground">Aucune donnée disponible</div>
  }

  return (
    <div className="space-y-4">
      {/* Timeline visuelle */}
      <div className="relative h-16 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
        {segments.map((segment, index) => {
          const widthPercent = totalDuration > 0 ? (segment.duration / totalDuration) * 100 : 0
          const color = stateColors[segment.state as keyof typeof stateColors] || "#6b7280"

          return (
            <div
              key={index}
              className="absolute top-0 h-full flex items-center justify-center text-white text-xs font-medium transition-all hover:opacity-80 cursor-pointer"
              style={{
                left: `${segments.slice(0, index).reduce((acc, s) => acc + (s.duration / totalDuration) * 100, 0)}%`,
                width: `${widthPercent}%`,
                backgroundColor: color,
                minWidth: widthPercent > 5 ? "auto" : "2px",
              }}
              title={`${stateLabels[segment.state as keyof typeof stateLabels]} - ${segment.duration.toFixed(1)} min`}
            >
              {widthPercent > 10 && (
                <span className="truncate px-1">{stateLabels[segment.state as keyof typeof stateLabels]}</span>
              )}
            </div>
          )
        })}
      </div>

      {/* Légende */}
      <div className="flex flex-wrap gap-4 text-sm">
        {Object.entries(stateColors).map(([state, color]) => (
          <div key={state} className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: color }} />
            <span className="text-muted-foreground">{stateLabels[state as keyof typeof stateLabels]}</span>
          </div>
        ))}
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        {segments.reduce(
          (acc, segment) => {
            const state = segment.state
            if (!acc[state]) {
              acc[state] = { duration: 0, count: 0 }
            }
            acc[state].duration += segment.duration
            acc[state].count += 1
            return acc
          },
          {} as Record<string, { duration: number; count: number }>,
        )}

        {Object.entries(
          segments.reduce(
            (acc, segment) => {
              const state = segment.state
              if (!acc[state]) {
                acc[state] = { duration: 0, count: 0 }
              }
              acc[state].duration += segment.duration
              acc[state].count += 1
              return acc
            },
            {} as Record<string, { duration: number; count: number }>,
          ),
        ).map(([state, stats]) => (
          <div key={state} className="text-center p-3 bg-muted rounded-lg">
            <div className="font-medium text-lg">{((stats.duration / totalDuration) * 100).toFixed(1)}%</div>
            <div className="text-muted-foreground text-xs">{stateLabels[state as keyof typeof stateLabels]}</div>
            <div className="text-muted-foreground text-xs">{stats.duration.toFixed(0)} min</div>
          </div>
        ))}
      </div>
    </div>
  )
}
