import { NextResponse } from "next/server"
import { database, firebaseConfig } from "@/lib/firebase"
import { ref, get } from "firebase/database"

export async function GET() {
  const healthCheck = {
    timestamp: new Date().toISOString(),
    status: "healthy",
    services: {
      firebase: {
        status: "unknown",
        configured: false,
        accessible: false,
        error: null as string | null
      },
      tandem: {
        status: "unknown",
        enabled: process.env.TANDEM_ENABLED === "true",
        configured: false,
        error: null as string | null
      }
    },
    environment: {
      nodeEnv: process.env.NODE_ENV,
      nextjsVersion: process.env.npm_package_dependencies_next || "unknown"
    }
  }

  // Check Firebase configuration
  try {
    healthCheck.services.firebase.configured = !!(
      firebaseConfig.apiKey && 
      firebaseConfig.databaseURL && 
      firebaseConfig.projectId
    )

    if (healthCheck.services.firebase.configured) {
      // Test Firebase connection with a simple check
      try {
        const testRef = ref(database, "current_data")
        await get(testRef)
        healthCheck.services.firebase.accessible = true
        healthCheck.services.firebase.status = "healthy"
      } catch (fbError) {
        // Firebase is configured but not accessible (likely permission issue)
        healthCheck.services.firebase.accessible = false
        healthCheck.services.firebase.status = "permission_denied"
        healthCheck.services.firebase.error = fbError instanceof Error ? fbError.message : "Firebase access denied"
      }
    } else {
      healthCheck.services.firebase.status = "misconfigured"
      healthCheck.services.firebase.error = "Missing Firebase configuration"
    }
  } catch (error) {
    healthCheck.services.firebase.status = "error"
    healthCheck.services.firebase.error = error instanceof Error ? error.message : "Unknown Firebase error"
  }

  // Check Tandem configuration
  try {
    if (healthCheck.services.tandem.enabled) {
      healthCheck.services.tandem.configured = !!(
        process.env.TANDEM_CLIENT_ID &&
        process.env.TANDEM_CLIENT_SECRET &&
        process.env.TANDEM_FACILITY_ID
      )

      if (healthCheck.services.tandem.configured) {
        healthCheck.services.tandem.status = "configured"
      } else {
        healthCheck.services.tandem.status = "misconfigured"
        healthCheck.services.tandem.error = "Missing Tandem configuration"
      }
    } else {
      healthCheck.services.tandem.status = "disabled"
    }
  } catch (error) {
    healthCheck.services.tandem.status = "error"
    healthCheck.services.tandem.error = error instanceof Error ? error.message : "Unknown Tandem error"
  }

  // Determine overall status
  const hasErrors = Object.values(healthCheck.services).some(service => service.status === "error")
  const hasMisconfigurations = Object.values(healthCheck.services).some(service => service.status === "misconfigured")

  if (hasErrors) {
    healthCheck.status = "error"
  } else if (hasMisconfigurations) {
    healthCheck.status = "warning"
  } else {
    healthCheck.status = "healthy"
  }

  const statusCode = healthCheck.status === "error" ? 500 : 
                    healthCheck.status === "warning" ? 200 : 200

  return NextResponse.json(healthCheck, { status: statusCode })
}
