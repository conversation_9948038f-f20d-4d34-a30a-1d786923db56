# Troubleshooting Guide - Predictive Maintenance Dashboard

## Common Issues and Solutions

### 1. Firebase Permission Denied Error

**Error**: `permission_denied at /current_data: Client doesn't have permission to access the desired data`

**Causes**:
- Firebase security rules are too restrictive
- Incorrect Firebase configuration
- Missing environment variables

**Solutions**:

1. **Check Firebase Security Rules**:
   - Go to your Firebase Console → Realtime Database → Rules
   - Ensure rules allow read/write access:
   ```json
   {
     "rules": {
       ".read": true,
       ".write": true
     }
   }
   ```

2. **Verify Environment Variables**:
   - Check `.env.local` file contains correct Firebase configuration
   - Ensure all required variables are set:
     - `FIREBASE_API_KEY`
     - `FIREBASE_DATABASE_URL`
     - `FIREBASE_PROJECT_ID`
     - `NEXT_PUBLIC_FIREBASE_API_KEY`
     - `NEXT_PUBLIC_FIREBASE_DATABASE_URL`
     - `NEXT_PUBLIC_FIREBASE_PROJECT_ID`

3. **Deploy Security Rules**:
   ```bash
   firebase deploy --only database
   ```

### 2. Tandem API 500 Error

**Error**: `Erreur API: 500` in `useTandemSync` hook

**Causes**:
- Tandem integration is disabled
- Missing Tandem credentials
- Invalid Tandem API credentials
- Network connectivity issues

**Solutions**:

1. **Enable Tandem Integration**:
   - Set `TANDEM_ENABLED=true` in `.env.local`

2. **Verify Tandem Credentials**:
   - Check all Tandem environment variables are set:
     - `TANDEM_CLIENT_ID`
     - `TANDEM_CLIENT_SECRET`
     - `TANDEM_FACILITY_ID`
     - `TANDEM_MODEL_ID`

3. **Test Tandem Authentication**:
   - Visit `/api/tandem/token` to test authentication
   - Should return access token if credentials are valid

4. **Disable Tandem if Not Needed**:
   - Set `TANDEM_ENABLED=false` in `.env.local`
   - Application will work without Tandem integration

### 3. Network and Connectivity Issues

**Symptoms**:
- Intermittent connection failures
- Data not updating in real-time
- API timeouts

**Solutions**:

1. **Check Internet Connection**
2. **Verify Firebase Database URL**
3. **Check Firewall Settings**
4. **Restart the Application**:
   ```bash
   npm run dev
   ```

### 4. Environment Configuration

**Required Environment Variables**:

```bash
# Firebase Configuration
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_DATABASE_URL=https://your-project.firebasedatabase.app/
FIREBASE_PROJECT_ID=your-project-id

# Public Firebase Configuration (for client-side)
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_DATABASE_URL=https://your-project.firebasedatabase.app/
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000

# Tandem Configuration (Optional)
TANDEM_CLIENT_ID=your_tandem_client_id
TANDEM_CLIENT_SECRET=your_tandem_client_secret
TANDEM_FACILITY_ID=your_facility_id
TANDEM_MODEL_ID=your_model_id
TANDEM_BASE_URL=https://developer.api.autodesk.com
TANDEM_ENABLED=false

# Public Tandem Configuration
NEXT_PUBLIC_TANDEM_FACILITY_ID=your_facility_id
NEXT_PUBLIC_TANDEM_MODEL_ID=your_model_id
```

### 5. Development Setup

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Start Development Server**:
   ```bash
   npm run dev
   ```

3. **Check Console for Errors**:
   - Open browser developer tools
   - Check Console tab for JavaScript errors
   - Check Network tab for failed API requests

### 6. Firebase Setup Steps

1. **Create Firebase Project**:
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Create new project or select existing one

2. **Enable Realtime Database**:
   - Go to Realtime Database section
   - Create database in test mode

3. **Configure Security Rules**:
   - Set appropriate security rules for your use case
   - For development, you can use permissive rules (not recommended for production)

4. **Get Configuration**:
   - Go to Project Settings → General
   - Copy Web API Key and other configuration details

### 7. Debugging Tips

1. **Enable Verbose Logging**:
   - Check browser console for detailed error messages
   - Look for Firebase and API error logs

2. **Test API Endpoints**:
   - Visit `/api/firebase` to test Firebase connection
   - Visit `/api/tandem/sync` to test Tandem integration

3. **Check Network Tab**:
   - Monitor API requests in browser developer tools
   - Look for failed requests and error responses

4. **Restart Services**:
   - Restart development server
   - Clear browser cache
   - Try incognito/private browsing mode

### 8. Getting Help

If you continue to experience issues:

1. Check the browser console for detailed error messages
2. Verify all environment variables are correctly set
3. Ensure Firebase project is properly configured
4. Test individual API endpoints
5. Check Firebase Console for any service issues

For additional support, please provide:
- Error messages from browser console
- Network tab screenshots showing failed requests
- Environment configuration (without sensitive credentials)
- Steps to reproduce the issue
