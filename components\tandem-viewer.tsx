"use client"

import { useEffect, useRef, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { RefreshCw, Eye, Settings, AlertTriangle, CheckCircle } from "lucide-react"
import { useTandemSync } from "@/hooks/use-tandem-sync"

interface TandemViewerProps {
  facilityId?: string
  modelId?: string
  className?: string
}

export function TandemViewer({ facilityId, modelId, className }: TandemViewerProps) {
  const viewerRef = useRef<HTMLDivElement>(null)
  const [isViewerLoaded, setIsViewerLoaded] = useState(false)
  const [viewerError, setViewerError] = useState<string | null>(null)
  const [selectedElement, setSelectedElement] = useState<string | null>(null)

  const { elements, isConnected, lastSync, syncData, loading, error: syncError } = useTandemSync()

  // Initialisation du viewer Tandem
  useEffect(() => {
    const initTandemViewer = async () => {
      try {
        // Vérifier si le SDK Tandem est disponible
        if (typeof window !== "undefined" && (window as any).Autodesk?.Tandem) {
          const viewer = new (window as any).Autodesk.Tandem.Viewer(viewerRef.current)

          // Configuration du viewer
          await viewer.initialize({
            facilityId: facilityId || process.env.NEXT_PUBLIC_TANDEM_FACILITY_ID,
            modelId: modelId || process.env.NEXT_PUBLIC_TANDEM_MODEL_ID,
            accessToken: await getTandemToken(),
          })

          // Événements du viewer
          viewer.addEventListener("elementSelected", (event: any) => {
            setSelectedElement(event.elementId)
          })

          viewer.addEventListener("viewerReady", () => {
            setIsViewerLoaded(true)
            setViewerError(null)
          })

          viewer.addEventListener("error", (error: any) => {
            setViewerError(error.message)
            setIsViewerLoaded(false)
          })

          // Appliquer les données en temps réel
          if (elements.length > 0) {
            updateViewerElements(viewer, elements)
          }
        } else {
          setViewerError("SDK Autodesk Tandem non disponible")
        }
      } catch (error) {
        console.error("Erreur d'initialisation Tandem:", error)
        setViewerError(error instanceof Error ? error.message : "Erreur d'initialisation")
      }
    }

    // Charger le SDK Tandem si nécessaire
    if (!document.querySelector('script[src*="tandem"]')) {
      const script = document.createElement("script")
      script.src = "https://developer.api.autodesk.com/tandem/v1/viewer.js"
      script.onload = () => initTandemViewer()
      script.onerror = () => setViewerError("Impossible de charger le SDK Tandem")
      document.head.appendChild(script)
    } else {
      initTandemViewer()
    }
  }, [facilityId, modelId])

  // Mise à jour des éléments quand les données changent
  useEffect(() => {
    if (isViewerLoaded && elements.length > 0) {
      // Mettre à jour les éléments dans le viewer
      updateViewerElements(viewerRef.current, elements)
    }
  }, [elements, isViewerLoaded])

  const updateViewerElements = (viewer: any, elements: any[]) => {
    elements.forEach((element) => {
      try {
        // Mettre à jour les propriétés de l'élément
        viewer.setElementProperties(element.elementId, element.properties)

        // Mettre à jour le statut visuel
        viewer.setElementStatus(element.elementId, element.status)

        // Ajouter les alertes
        if (element.alerts && element.alerts.length > 0) {
          viewer.addElementAlerts(element.elementId, element.alerts)
        }
      } catch (error) {
        console.error(`Erreur mise à jour élément ${element.elementId}:`, error)
      }
    })
  }

  const getTandemToken = async () => {
    try {
      const response = await fetch("/api/tandem/token")
      const data = await response.json()
      return data.access_token
    } catch (error) {
      console.error("Erreur récupération token:", error)
      return null
    }
  }

  const handleRefresh = () => {
    syncData()
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "normal":
        return "bg-green-500"
      case "warning":
        return "bg-yellow-500"
      case "error":
        return "bg-red-500"
      case "stopped":
        return "bg-blue-500"
      default:
        return "bg-gray-500"
    }
  }

  return (
    <div className={className}>
      <Tabs defaultValue="viewer" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="viewer">🏗️ Viewer 3D</TabsTrigger>
          <TabsTrigger value="elements">📋 Éléments</TabsTrigger>
          <TabsTrigger value="sync">🔄 Synchronisation</TabsTrigger>
        </TabsList>

        <TabsContent value="viewer" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-5 w-5" />
                    Viewer Autodesk Tandem
                  </CardTitle>
                  <CardDescription>Visualisation 3D du projet Revit avec données temps réel</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={isConnected ? "default" : "destructive"}>
                    {isConnected ? "🟢 Connecté" : "🔴 Déconnecté"}
                  </Badge>
                  <Button onClick={handleRefresh} disabled={loading} size="sm">
                    <RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {viewerError ? (
                <Alert className="border-red-500">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Erreur Tandem:</strong> {viewerError}
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="space-y-4">
                  {/* Container du viewer Tandem */}
                  <div
                    ref={viewerRef}
                    className="w-full h-96 border rounded-lg bg-gray-100 dark:bg-gray-800"
                    style={{ minHeight: "400px" }}
                  >
                    {!isViewerLoaded && (
                      <div className="flex items-center justify-center h-full">
                        <div className="text-center">
                          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
                          <p className="text-muted-foreground">Chargement du viewer Tandem...</p>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Informations sur l'élément sélectionné */}
                  {selectedElement && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Élément Sélectionné</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div>
                            <strong>ID:</strong> {selectedElement}
                          </div>
                          {elements.find((e) => e.elementId === selectedElement) && (
                            <div className="grid gap-2">
                              <div>
                                <strong>Statut:</strong>
                                <Badge className="ml-2" variant="outline">
                                  {elements.find((e) => e.elementId === selectedElement)?.status}
                                </Badge>
                              </div>
                              <div>
                                <strong>Propriétés:</strong>
                              </div>
                              <div className="ml-4 space-y-1 text-sm">
                                {Object.entries(
                                  elements.find((e) => e.elementId === selectedElement)?.properties || {},
                                ).map(([key, value]) => (
                                  <div key={key}>
                                    {key}: {String(value)}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="elements" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>📋 Éléments Synchronisés</CardTitle>
              <CardDescription>Liste des éléments du modèle avec leurs données temps réel</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {elements.length > 0 ? (
                  elements.map((element) => (
                    <div key={element.elementId} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold">{element.elementId}</h4>
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full ${getStatusColor(element.status)}`} />
                          <Badge variant="outline">{element.status}</Badge>
                        </div>
                      </div>

                      <div className="grid gap-2 md:grid-cols-2 text-sm">
                        {Object.entries(element.properties).map(([key, value]) => (
                          <div key={key}>
                            <span className="font-medium">{key}:</span> {String(value)}
                          </div>
                        ))}
                      </div>

                      {element.alerts && element.alerts.length > 0 && (
                        <div className="mt-2">
                          <div className="text-sm font-medium mb-1">Alertes:</div>
                          {element.alerts.map((alert, index) => (
                            <Badge key={index} variant="destructive" className="mr-1">
                              {alert.message}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">Aucun élément synchronisé</div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sync" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Synchronisation Tandem
              </CardTitle>
              <CardDescription>Configuration et statut de la synchronisation avec Autodesk Tandem</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Statut de connexion */}
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <div className="font-medium">Statut de connexion</div>
                    <div className="text-sm text-muted-foreground">Connexion avec l'API Autodesk Tandem</div>
                  </div>
                  <div className="flex items-center gap-2">
                    {isConnected ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-5 w-5 text-red-500" />
                    )}
                    <Badge variant={isConnected ? "default" : "destructive"}>
                      {isConnected ? "Connecté" : "Déconnecté"}
                    </Badge>
                  </div>
                </div>

                {/* Dernière synchronisation */}
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <div className="font-medium">Dernière synchronisation</div>
                    <div className="text-sm text-muted-foreground">
                      {lastSync ? new Date(lastSync).toLocaleString() : "Jamais"}
                    </div>
                  </div>
                  <Button onClick={handleRefresh} disabled={loading}>
                    <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
                    Synchroniser
                  </Button>
                </div>

                {/* Erreurs */}
                {syncError && (
                  <Alert className="border-red-500">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Erreur de synchronisation:</strong> {syncError}
                    </AlertDescription>
                  </Alert>
                )}

                {/* Configuration */}
                <div className="space-y-2">
                  <div className="font-medium">Configuration</div>
                  <div className="grid gap-2 text-sm">
                    <div>
                      <strong>Facility ID:</strong>{" "}
                      {facilityId || process.env.NEXT_PUBLIC_TANDEM_FACILITY_ID || "Non configuré"}
                    </div>
                    <div>
                      <strong>Model ID:</strong> {modelId || process.env.NEXT_PUBLIC_TANDEM_MODEL_ID || "Non configuré"}
                    </div>
                    <div>
                      <strong>Éléments synchronisés:</strong> {elements.length}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
