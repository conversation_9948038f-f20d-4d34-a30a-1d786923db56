"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { TestSimulator } from "@/components/test-simulator"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Activity, Zap, AlertTriangle, CheckCircle } from "lucide-react"

export default function TestPage() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">🧪 Centre de Test</h2>
          <p className="text-muted-foreground">Simulation et test du flux de données sans capteur physique</p>
        </div>
        <Badge variant="secondary" className="pulse-glow">
          🧪 Mode Test
        </Badge>
      </div>

      <Tabs defaultValue="simulator" className="space-y-4">
        <TabsList>
          <TabsTrigger value="simulator">🎮 Simulateur</TabsTrigger>
          <TabsTrigger value="endpoints">📡 Endpoints</TabsTrigger>
          <TabsTrigger value="curl">💻 Commandes cURL</TabsTrigger>
        </TabsList>

        <TabsContent value="simulator" className="space-y-4">
          <TestSimulator />
        </TabsContent>

        <TabsContent value="endpoints" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Endpoints de Test
                </CardTitle>
                <CardDescription>APIs disponibles pour tester le flux de données</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="p-3 border rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="default">POST</Badge>
                      <code className="text-sm">/api/test/simulate</code>
                    </div>
                    <p className="text-sm text-muted-foreground">Génère un point de données aléatoire</p>
                  </div>

                  <div className="p-3 border rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="default">POST</Badge>
                      <code className="text-sm">/api/test/simulate-batch</code>
                    </div>
                    <p className="text-sm text-muted-foreground">Génère plusieurs points selon un scénario</p>
                  </div>

                  <div className="p-3 border rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="default">POST</Badge>
                      <code className="text-sm">/api/test/node-red-simulate</code>
                    </div>
                    <p className="text-sm text-muted-foreground">Teste l'endpoint Node-RED</p>
                  </div>

                  <div className="p-3 border rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="secondary">POST</Badge>
                      <code className="text-sm">/api/data</code>
                    </div>
                    <p className="text-sm text-muted-foreground">Endpoint principal (format ESP32)</p>
                  </div>

                  <div className="p-3 border rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="secondary">POST</Badge>
                      <code className="text-sm">/api/node-red</code>
                    </div>
                    <p className="text-sm text-muted-foreground">Endpoint Node-RED</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  Flux de Test
                </CardTitle>
                <CardDescription>Vérification du flux complet</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span className="text-sm">1. Génération des données simulées</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span className="text-sm">2. Traitement et validation</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span className="text-sm">3. Sauvegarde Firebase</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <span className="text-sm">4. Synchronisation Tandem (si activé)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span className="text-sm">5. Affichage temps réel dashboard</span>
                  </div>
                </div>

                <Alert>
                  <Zap className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Conseil :</strong> Utilisez le simulateur temps réel pour voir les données s'afficher en
                    direct sur le dashboard principal.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="curl" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Commandes cURL
              </CardTitle>
              <CardDescription>Testez les endpoints directement depuis votre terminal</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">1. Test simple (format ESP32)</h4>
                  <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
                    {`curl -X POST http://localhost:3000/api/data \\
  -H "Content-Type: application/json" \\
  -d '{"x": 1200, "y": 800, "z": 1500}'`}
                  </pre>
                </div>

                <div>
                  <h4 className="font-medium mb-2">2. Test Node-RED</h4>
                  <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
                    {`curl -X POST http://localhost:3000/api/node-red \\
  -H "Content-Type: application/json" \\
  -d '{
    "payload": {
      "vibration_data": {"x": 1.2, "y": 0.8, "z": 1.5},
      "machine_state": "en_marche"
    },
    "topic": "test/vibration"
  }'`}
                  </pre>
                </div>

                <div>
                  <h4 className="font-medium mb-2">3. Simulation automatique</h4>
                  <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
                    {`curl -X POST http://localhost:3000/api/test/simulate`}
                  </pre>
                </div>

                <div>
                  <h4 className="font-medium mb-2">4. Simulation batch (scénario panne)</h4>
                  <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
                    {`curl -X POST http://localhost:3000/api/test/simulate-batch \\
  -H "Content-Type: application/json" \\
  -d '{
    "count": 20,
    "interval": 1000,
    "scenario": "panne_progressive"
  }'`}
                  </pre>
                </div>

                <div>
                  <h4 className="font-medium mb-2">5. Vérifier les données Firebase</h4>
                  <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
                    {`curl http://localhost:3000/api/firebase?hours=1`}
                  </pre>
                </div>
              </div>

              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Note :</strong> Remplacez <code>localhost:3000</code> par l'URL de votre application si elle
                  est déployée ailleurs.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
