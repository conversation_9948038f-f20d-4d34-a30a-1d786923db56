"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Play, Square, RotateCcw, Zap, Activity, AlertTriangle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export function TestSimulator() {
  const { toast } = useToast()
  const [isRunning, setIsRunning] = useState(false)
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null)
  const [simulationCount, setSimulationCount] = useState(0)
  const [lastResult, setLastResult] = useState<any>(null)

  // Paramètres de simulation
  const [interval, setInterval] = useState(2000) // 2 secondes
  const [scenario, setScenario] = useState("normal")
  const [batchCount, setBatchCount] = useState(10)

  const scenarios = {
    normal: "Fonctionnement normal",
    panne_progressive: "Panne progressive",
    arret_redemarrage: "Arrêt puis redémarrage",
    vibrations_cycliques: "Vibrations cycliques",
    aleatoire: "Données complètement aléatoires",
  }

  const startSimulation = () => {
    if (isRunning) return

    setIsRunning(true)
    setSimulationCount(0)

    const id = setInterval(async () => {
      try {
        const response = await fetch("/api/test/simulate", {
          method: "POST",
        })

        const result = await response.json()

        if (result.success) {
          setSimulationCount((prev) => prev + 1)
          setLastResult(result)
        } else {
          throw new Error(result.error || "Erreur de simulation")
        }
      } catch (error) {
        console.error("Erreur simulation:", error)
        toast({
          title: "Erreur de simulation",
          description: error instanceof Error ? error.message : "Erreur inconnue",
          variant: "destructive",
        })
        stopSimulation()
      }
    }, interval)

    setIntervalId(id)

    toast({
      title: "Simulation démarrée",
      description: `Génération de données toutes les ${interval / 1000} secondes`,
    })
  }

  const stopSimulation = () => {
    if (intervalId) {
      clearInterval(intervalId)
      setIntervalId(null)
    }
    setIsRunning(false)

    toast({
      title: "Simulation arrêtée",
      description: `${simulationCount} points de données générés`,
    })
  }

  const sendSingleData = async () => {
    try {
      const response = await fetch("/api/test/simulate", {
        method: "POST",
      })

      const result = await response.json()

      if (result.success) {
        setLastResult(result)
        toast({
          title: "Données envoyées",
          description: "Point de données unique généré avec succès",
        })
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Erreur d'envoi",
        variant: "destructive",
      })
    }
  }

  const sendBatchData = async () => {
    try {
      const response = await fetch("/api/test/simulate-batch", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          count: batchCount,
          interval: 500, // 0.5 seconde entre chaque point
          scenario,
        }),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Batch envoyé",
          description: `${batchCount} points de données générés avec le scénario "${scenarios[scenario as keyof typeof scenarios]}"`,
        })
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      toast({
        title: "Erreur batch",
        description: error instanceof Error ? error.message : "Erreur d'envoi batch",
        variant: "destructive",
      })
    }
  }

  const testNodeRed = async () => {
    try {
      const response = await fetch("/api/test/node-red-simulate", {
        method: "POST",
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Test Node-RED réussi",
          description: "Données Node-RED simulées envoyées avec succès",
        })
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      toast({
        title: "Erreur Node-RED",
        description: error instanceof Error ? error.message : "Erreur test Node-RED",
        variant: "destructive",
      })
    }
  }

  const resetSimulation = () => {
    stopSimulation()
    setSimulationCount(0)
    setLastResult(null)
  }

  return (
    <div className="space-y-4">
      <Tabs defaultValue="realtime" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="realtime">🔄 Temps Réel</TabsTrigger>
          <TabsTrigger value="batch">📦 Batch</TabsTrigger>
          <TabsTrigger value="node-red">📡 Node-RED</TabsTrigger>
        </TabsList>

        <TabsContent value="realtime" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Simulation Temps Réel
              </CardTitle>
              <CardDescription>Génération continue de données de capteur simulées</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="interval">Intervalle (ms)</Label>
                  <Input
                    id="interval"
                    type="number"
                    value={interval}
                    onChange={(e) => setInterval(Number(e.target.value))}
                    min="500"
                    max="10000"
                    step="500"
                    disabled={isRunning}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Statut</Label>
                  <div className="flex items-center gap-2">
                    <Badge variant={isRunning ? "default" : "secondary"}>
                      {isRunning ? "🟢 En cours" : "🔴 Arrêté"}
                    </Badge>
                    <Badge variant="outline">{simulationCount} points générés</Badge>
                  </div>
                </div>
              </div>

              <div className="flex gap-2">
                <Button onClick={startSimulation} disabled={isRunning}>
                  <Play className="mr-2 h-4 w-4" />
                  Démarrer
                </Button>
                <Button onClick={stopSimulation} disabled={!isRunning} variant="outline">
                  <Square className="mr-2 h-4 w-4" />
                  Arrêter
                </Button>
                <Button onClick={resetSimulation} variant="outline">
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Reset
                </Button>
                <Button onClick={sendSingleData} variant="secondary">
                  <Zap className="mr-2 h-4 w-4" />
                  Point unique
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="batch" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Simulation par Scénarios
              </CardTitle>
              <CardDescription>Génération de données selon des scénarios prédéfinis</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="scenario">Scénario</Label>
                  <Select value={scenario} onValueChange={setScenario}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(scenarios).map(([key, label]) => (
                        <SelectItem key={key} value={key}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="batchCount">Nombre de points</Label>
                  <Input
                    id="batchCount"
                    type="number"
                    value={batchCount}
                    onChange={(e) => setBatchCount(Number(e.target.value))}
                    min="1"
                    max="100"
                  />
                </div>
              </div>

              <Button onClick={sendBatchData} className="w-full">
                Générer le scénario "{scenarios[scenario as keyof typeof scenarios]}"
              </Button>

              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Scénarios disponibles :</strong>
                  <ul className="mt-2 space-y-1 text-sm">
                    <li>
                      • <strong>Normal :</strong> Vibrations normales de fonctionnement
                    </li>
                    <li>
                      • <strong>Panne progressive :</strong> Dégradation progressive des vibrations
                    </li>
                    <li>
                      • <strong>Arrêt/Redémarrage :</strong> Simulation d'un cycle d'arrêt
                    </li>
                    <li>
                      • <strong>Cycliques :</strong> Vibrations suivant un pattern répétitif
                    </li>
                  </ul>
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="node-red" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Test Node-RED
              </CardTitle>
              <CardDescription>Test de l'endpoint Node-RED avec données simulées</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button onClick={testNodeRed} className="w-full">
                Tester l'endpoint Node-RED
              </Button>

              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Format Node-RED testé :</strong>
                  <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-x-auto">
                    {`{
  "payload": {
    "vibration_data": {
      "x": 1.23, "y": 0.87, "z": 2.15
    },
    "machine_state": "en_marche",
    "additional_data": {
      "temperature": 45.2,
      "pressure": 2.1
    }
  },
  "topic": "machine/vibration/test"
}`}
                  </pre>
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Affichage des derniers résultats */}
      {lastResult && (
        <Card>
          <CardHeader>
            <CardTitle>Dernières Données Générées</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2 text-sm">
              <div>
                <strong>Données ESP32 simulées :</strong>
              </div>
              <div className="ml-4 space-y-1">
                <div>X: {lastResult.simulatedData?.original?.x || "N/A"}</div>
                <div>Y: {lastResult.simulatedData?.original?.y || "N/A"}</div>
                <div>Z: {lastResult.simulatedData?.original?.z || "N/A"}</div>
              </div>
              <div>
                <strong>Données traitées :</strong>
              </div>
              <div className="ml-4 space-y-1">
                <div>Vibration X: {lastResult.simulatedData?.processed?.vibration_x?.toFixed(3) || "N/A"} mm/s</div>
                <div>Vibration Y: {lastResult.simulatedData?.processed?.vibration_y?.toFixed(3) || "N/A"} mm/s</div>
                <div>Vibration Z: {lastResult.simulatedData?.processed?.vibration_z?.toFixed(3) || "N/A"} mm/s</div>
                <div>État: {lastResult.simulatedData?.processed?.etat_machine || "N/A"}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
