import type React from "react"
import { Card, CardContent } from "@/components/ui/card"
import { TrendingUp, TrendingDown } from "lucide-react"
import { cn } from "@/lib/utils"

interface MetricCardProps {
  title: string
  value: string
  icon: React.ReactNode
  className?: string
  trend?: "up" | "down"
  delta?: string
}

export function MetricCard({ title, value, icon, className, trend, delta }: MetricCardProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between space-y-0 pb-2">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <div className={cn("text-muted-foreground", className)}>{icon}</div>
        </div>
        <div className="space-y-1">
          <p className="text-2xl font-bold">{value}</p>
          {(trend || delta) && (
            <div className="flex items-center space-x-1 text-xs text-muted-foreground">
              {trend && (
                <>
                  {trend === "up" ? (
                    <TrendingUp className="h-3 w-3 text-green-500" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500" />
                  )}
                </>
              )}
              {delta && <span>{delta}</span>}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
