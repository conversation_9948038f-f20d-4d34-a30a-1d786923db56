import { NextResponse } from "next/server"

export async function GET() {
  try {
    const response = await fetch("https://developer.api.autodesk.com/authentication/v2/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        grant_type: "client_credentials",
        client_id: process.env.TANDEM_CLIENT_ID!,
        client_secret: process.env.TANDEM_CLIENT_SECRET!,
        scope: "data:read data:write",
      }),
    })

    if (!response.ok) {
      throw new Error(`Erreur d'authentification: ${response.status}`)
    }

    const data = await response.json()

    return NextResponse.json({
      access_token: data.access_token,
      expires_in: data.expires_in,
      token_type: data.token_type,
    })
  } catch (error) {
    console.error("Erreur token Tandem:", error)
    return NextResponse.json(
      {
        error: "Impossible d'obtenir le token Tandem",
      },
      { status: 500 },
    )
  }
}
