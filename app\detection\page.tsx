"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Search, CheckCircle, AlertTriangle, Activity } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useRealTimeData } from "@/hooks/use-real-time-data"

interface DetectedStop {
  id: string
  debut_arret: string
  fin_arret: string
  duree_minutes: number
  statut: "detecte_auto" | "classifie"
  classifie: boolean
  type_arret?: string
  sous_categorie?: string
  commentaire?: string
  operateur?: string
  urgence?: string
}

const typesArrets = {
  arret_maintenance: {
    label: "Arrêt Maintenance",
    sous_categories: ["Maintenance préventive", "Maintenance corrective", "Changement d'outil", "Nettoyage"],
  },
  arret_prevu: {
    label: "Arrêt Prévu",
    sous_categories: ["Pause déjeuner", "Fin d'équipe", "Changement d'équipe", "Formation"],
  },
  arret_imprevu: {
    label: "Arrêt Imprévu",
    sous_categories: ["Panne électrique", "Panne mécanique", "Problème matière première", "Incident sécurité"],
  },
  arret_conducteur: {
    label: "Arrêt Conducteur",
    sous_categories: ["Absence opérateur", "Formation opérateur", "Réglage machine", "Contrôle qualité"],
  },
  arret_qualite: {
    label: "Arrêt Qualité",
    sous_categories: ["Défaut produit", "Contrôle qualité", "Rejet client", "Non-conformité"],
  },
}

const niveauxUrgence = ["Faible", "Moyen", "Élevé", "Critique"]

export default function DetectionPage() {
  const { toast } = useToast()
  const { data: realtimeData } = useRealTimeData()
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [detectedStops, setDetectedStops] = useState<DetectedStop[]>([
    {
      id: "1",
      debut_arret: new Date(Date.now() - 7200000).toISOString(),
      fin_arret: new Date(Date.now() - 6900000).toISOString(),
      duree_minutes: 5,
      statut: "detecte_auto",
      classifie: false,
    },
    {
      id: "2",
      debut_arret: new Date(Date.now() - 3600000).toISOString(),
      fin_arret: new Date(Date.now() - 3300000).toISOString(),
      duree_minutes: 5,
      statut: "detecte_auto",
      classifie: false,
    },
    {
      id: "3",
      debut_arret: new Date(Date.now() - 1800000).toISOString(),
      fin_arret: new Date(Date.now() - 1500000).toISOString(),
      duree_minutes: 5,
      statut: "detecte_auto",
      classifie: false,
    },
  ])

  const [classificationForms, setClassificationForms] = useState<Record<string, any>>({})

  // Déterminer l'état actuel de la machine
  const currentVibration = realtimeData
    ? Math.sqrt(realtimeData.vibration_x ** 2 + realtimeData.vibration_y ** 2 + realtimeData.vibration_z ** 2)
    : 0

  const seuilArret = 0.1
  const machineArretee = currentVibration <= seuilArret

  const handleAnalyze = async () => {
    setIsAnalyzing(true)
    setProgress(0)

    // Simulation de l'analyse
    for (let i = 0; i <= 100; i += 10) {
      setProgress(i)
      await new Promise((resolve) => setTimeout(resolve, 200))
    }

    // Simulation de nouveaux arrêts détectés
    const newStop: DetectedStop = {
      id: Date.now().toString(),
      debut_arret: new Date(Date.now() - 600000).toISOString(),
      fin_arret: new Date(Date.now() - 300000).toISOString(),
      duree_minutes: 5,
      statut: "detecte_auto",
      classifie: false,
    }

    setDetectedStops((prev) => [newStop, ...prev])
    setIsAnalyzing(false)

    toast({
      title: "Analyse terminée",
      description: `1 nouvel arrêt détecté. Total: ${detectedStops.length + 1} arrêts non classifiés.`,
    })
  }

  const handleClassification = async (stopId: string) => {
    const formData = classificationForms[stopId]
    if (!formData?.type_arret || !formData?.sous_categorie) {
      toast({
        title: "Erreur",
        description: "Veuillez remplir tous les champs obligatoires.",
        variant: "destructive",
      })
      return
    }

    // Simulation de la classification
    setDetectedStops((prev) =>
      prev.map((stop) =>
        stop.id === stopId ? { ...stop, ...formData, classifie: true, statut: "classifie" as const } : stop,
      ),
    )

    // Nettoyer le formulaire
    setClassificationForms((prev) => {
      const newForms = { ...prev }
      delete newForms[stopId]
      return newForms
    })

    toast({
      title: "Arrêt classifié",
      description: "L'arrêt a été classifié avec succès.",
    })
  }

  const updateClassificationForm = (stopId: string, field: string, value: string) => {
    setClassificationForms((prev) => ({
      ...prev,
      [stopId]: {
        ...prev[stopId],
        [field]: value,
      },
    }))
  }

  const arrets_non_classifies = detectedStops.filter((stop) => !stop.classifie)
  const arrets_classifies = detectedStops.filter((stop) => stop.classifie)

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Détection Automatique d'Arrêts</h2>
          <p className="text-muted-foreground">
            Analyse automatique des signaux de vibration pour détecter les arrêts machine
          </p>
        </div>
      </div>

      {/* Flux de détection */}
      <Card>
        <CardHeader>
          <CardTitle>🔄 Flux de Détection</CardTitle>
          <CardDescription>Logique de détection basée sur l'analyse des signaux de vibration</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                  1
                </div>
                <h4 className="font-semibold">Signal de vibration</h4>
              </div>
              <p className="text-sm text-muted-foreground">Analyse continue des signaux X, Y, Z des capteurs</p>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                  2
                </div>
                <h4 className="font-semibold">Décision: Signal = 0 ?</h4>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-2 h-2 bg-red-500 rounded-full" />
                  <span>Signal = 0 → Machine arrêtée</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span>Signal ≠ 0 → Machine en marche</span>
                </div>
              </div>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                  3
                </div>
                <h4 className="font-semibold">Classification</h4>
              </div>
              <p className="text-sm text-muted-foreground">Demande de classification du motif d'arrêt</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* État actuel */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>📊 État Actuel</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Vibration totale:</span>
                <Badge variant={currentVibration > seuilArret ? "default" : "destructive"}>
                  {currentVibration.toFixed(3)} mm/s
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Seuil de détection:</span>
                <Badge variant="outline">{seuilArret} mm/s</Badge>
              </div>

              <Alert className={machineArretee ? "border-red-500" : "border-green-500"}>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {machineArretee ? (
                    <span className="text-red-600 font-medium">
                      ⚠️ Machine actuellement arrêtée - Signal proche de zéro
                    </span>
                  ) : (
                    <span className="text-green-600 font-medium">✅ Machine en fonctionnement - Signal détecté</span>
                  )}
                </AlertDescription>
              </Alert>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>📈 Statistiques</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 grid-cols-2">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{detectedStops.length}</div>
                <div className="text-sm text-muted-foreground">Arrêts détectés</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{arrets_non_classifies.length}</div>
                <div className="text-sm text-muted-foreground">Non classifiés</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{arrets_classifies.length}</div>
                <div className="text-sm text-muted-foreground">Classifiés</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {detectedStops.length > 0 ? ((arrets_classifies.length / detectedStops.length) * 100).toFixed(1) : 0}%
                </div>
                <div className="text-sm text-muted-foreground">Taux classification</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lancer détection */}
      <Card>
        <CardHeader>
          <CardTitle>🔍 Lancer une Détection</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button onClick={handleAnalyze} disabled={isAnalyzing} className="w-full md:w-auto">
              <Search className="mr-2 h-4 w-4" />
              {isAnalyzing ? "Analyse en cours..." : "Analyser les Signaux de Vibration"}
            </Button>

            {isAnalyzing && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Progression de l'analyse</span>
                  <span>{progress}%</span>
                </div>
                <Progress value={progress} className="w-full" />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Arrêts non classifiés */}
      {arrets_non_classifies.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
              Arrêts Détectés Nécessitant une Classification
            </CardTitle>
            <CardDescription>
              Les arrêts suivants ont été détectés automatiquement et nécessitent une classification
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {arrets_non_classifies.map((stop) => (
                <div key={stop.id} className="border rounded-lg p-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <h4 className="font-semibold mb-2">
                        🔍 Arrêt #{stop.id} - {stop.duree_minutes} minutes
                      </h4>
                      <div className="space-y-1 text-sm text-muted-foreground">
                        <div>
                          <strong>Début:</strong> {new Date(stop.debut_arret).toLocaleString()}
                        </div>
                        <div>
                          <strong>Fin:</strong> {new Date(stop.fin_arret).toLocaleString()}
                        </div>
                        <div>
                          <strong>Durée:</strong> {stop.duree_minutes} minutes
                        </div>
                        <Badge variant="destructive">Non classifié</Badge>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h5 className="font-medium">Classification de l'arrêt:</h5>

                      <div className="space-y-2">
                        <Label>Type d'arrêt</Label>
                        <Select
                          value={classificationForms[stop.id]?.type_arret || ""}
                          onValueChange={(value) => updateClassificationForm(stop.id, "type_arret", value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionnez le type" />
                          </SelectTrigger>
                          <SelectContent>
                            {Object.entries(typesArrets).map(([key, type]) => (
                              <SelectItem key={key} value={key}>
                                {type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {classificationForms[stop.id]?.type_arret && (
                        <div className="space-y-2">
                          <Label>Sous-catégorie</Label>
                          <Select
                            value={classificationForms[stop.id]?.sous_categorie || ""}
                            onValueChange={(value) => updateClassificationForm(stop.id, "sous_categorie", value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Sélectionnez la sous-catégorie" />
                            </SelectTrigger>
                            <SelectContent>
                              {typesArrets[
                                classificationForms[stop.id].type_arret as keyof typeof typesArrets
                              ]?.sous_categories.map((cat) => (
                                <SelectItem key={cat} value={cat}>
                                  {cat}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      )}

                      <div className="space-y-2">
                        <Label>Opérateur</Label>
                        <Input
                          value={classificationForms[stop.id]?.operateur || ""}
                          onChange={(e) => updateClassificationForm(stop.id, "operateur", e.target.value)}
                          placeholder="Nom de l'opérateur"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Niveau d'urgence</Label>
                        <Select
                          value={classificationForms[stop.id]?.urgence || "Moyen"}
                          onValueChange={(value) => updateClassificationForm(stop.id, "urgence", value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {niveauxUrgence.map((niveau) => (
                              <SelectItem key={niveau} value={niveau}>
                                {niveau}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Commentaire</Label>
                        <Textarea
                          value={classificationForms[stop.id]?.commentaire || ""}
                          onChange={(e) => updateClassificationForm(stop.id, "commentaire", e.target.value)}
                          placeholder="Commentaire sur l'arrêt..."
                          rows={3}
                        />
                      </div>

                      <Button onClick={() => handleClassification(stop.id)} className="w-full">
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Classifier cet arrêt
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Arrêts classifiés */}
      {arrets_classifies.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Arrêts Classifiés
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {arrets_classifies.map((stop) => (
                <div key={stop.id} className="border rounded-lg p-4 bg-green-50 dark:bg-green-900/10">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold">
                      Arrêt #{stop.id} - {stop.duree_minutes} minutes
                    </h4>
                    <Badge variant="default">Classifié</Badge>
                  </div>
                  <div className="grid gap-2 md:grid-cols-3 text-sm">
                    <div>
                      <strong>Type:</strong> {typesArrets[stop.type_arret as keyof typeof typesArrets]?.label}
                    </div>
                    <div>
                      <strong>Sous-catégorie:</strong> {stop.sous_categorie}
                    </div>
                    <div>
                      <strong>Opérateur:</strong> {stop.operateur}
                    </div>
                  </div>
                  {stop.commentaire && (
                    <div className="mt-2 text-sm">
                      <strong>Commentaire:</strong> {stop.commentaire}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {arrets_non_classifies.length === 0 && detectedStops.length === 0 && (
        <Alert>
          <Activity className="h-4 w-4" />
          <AlertDescription>
            Aucun arrêt détecté pour le moment. Cliquez sur "Analyser les Signaux de Vibration" pour détecter
            automatiquement les arrêts.
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
