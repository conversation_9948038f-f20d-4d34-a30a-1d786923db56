import { NextResponse } from "next/server"

export async function POST(request: Request) {
  try {
    const { count = 10, interval = 1000, scenario = "normal" } = await request.json()

    const results = []

    for (let i = 0; i < count; i++) {
      const data = generateScenarioData(scenario, i)

      // Envoyer vers l'API
      const response = await fetch(`${process.env.NEXTAUTH_URL || "http://localhost:3000"}/api/data`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()
      results.push({
        index: i,
        data,
        result: result.success,
        timestamp: new Date().toISOString(),
      })

      // Attendre entre les envois si spécifié
      if (i < count - 1 && interval > 0) {
        await new Promise((resolve) => setTimeout(resolve, interval))
      }
    }

    return NextResponse.json({
      success: true,
      message: `${count} points de données simulés envoyés`,
      scenario,
      results,
    })
  } catch (error) {
    console.error("Erreur simulation batch:", error)
    return NextResponse.json(
      {
        error: "Erreur lors de la simulation batch",
        details: error instanceof Error ? error.message : "Erreur inconnue",
      },
      { status: 500 },
    )
  }
}

function generateScenarioData(scenario: string, index: number) {
  const time = Date.now() + index * 1000

  switch (scenario) {
    case "panne_progressive":
      // Simulation d'une panne qui s'aggrave progressivement
      const severity = Math.min(index / 10, 1) // Augmente progressivement
      return {
        x: Math.round((1.0 + severity * 4.0 + Math.random() * 0.5) * 1000),
        y: Math.round((1.0 + severity * 4.0 + Math.random() * 0.5) * 1000),
        z: Math.round((1.0 + severity * 3.0 + Math.random() * 0.5) * 1000),
      }

    case "arret_redemarrage":
      // Simulation d'un arrêt puis redémarrage
      if (index < 5) {
        // Machine en marche
        return {
          x: Math.round((1.0 + Math.random() * 0.5) * 1000),
          y: Math.round((1.0 + Math.random() * 0.5) * 1000),
          z: Math.round((1.0 + Math.random() * 0.5) * 1000),
        }
      } else if (index < 8) {
        // Machine arrêtée
        return {
          x: Math.round((0.01 + Math.random() * 0.05) * 1000),
          y: Math.round((0.01 + Math.random() * 0.05) * 1000),
          z: Math.round((0.01 + Math.random() * 0.05) * 1000),
        }
      } else {
        // Machine redémarre
        return {
          x: Math.round((0.8 + Math.random() * 0.4) * 1000),
          y: Math.round((0.8 + Math.random() * 0.4) * 1000),
          z: Math.round((0.8 + Math.random() * 0.4) * 1000),
        }
      }

    case "vibrations_cycliques":
      // Vibrations qui suivent un pattern cyclique
      const cycle = Math.sin((index * Math.PI) / 5) // Cycle de 10 points
      const baseLevel = 1.0 + cycle * 0.8
      return {
        x: Math.round((baseLevel + Math.random() * 0.2) * 1000),
        y: Math.round((baseLevel + Math.random() * 0.2) * 1000),
        z: Math.round((baseLevel + Math.random() * 0.2) * 1000),
      }

    default: // "normal"
      return {
        x: Math.round((0.8 + Math.random() * 0.8) * 1000),
        y: Math.round((0.8 + Math.random() * 0.8) * 1000),
        z: Math.round((0.8 + Math.random() * 0.8) * 1000),
      }
  }
}
