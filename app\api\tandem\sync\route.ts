import { type NextRequest, NextResponse } from "next/server"

// Configuration Autodesk Tandem
const TANDEM_CONFIG = {
  baseUrl: process.env.TANDEM_BASE_URL || "https://developer.api.autodesk.com",
  clientId: process.env.TANDEM_CLIENT_ID,
  clientSecret: process.env.TANDEM_CLIENT_SECRET,
  facilityId: process.env.TANDEM_FACILITY_ID,
  modelId: process.env.TANDEM_MODEL_ID,
}

interface TandemElement {
  elementId: string
  properties: Record<string, any>
  status: string
  alerts: any[]
}

export async function POST(request: NextRequest) {
  try {
    const machineData = await request.json()

    // Obtenir le token d'accès Tandem
    const accessToken = await getTandemAccessToken()

    if (!accessToken) {
      return NextResponse.json({ error: "Impossible d'obtenir le token Tandem" }, { status: 401 })
    }

    // Mapper les données machine vers les éléments Tandem
    const tandemElements = mapMachineDataToTandem(machineData)

    // Synchroniser avec Tandem
    const syncResults = await Promise.all(tandemElements.map((element) => syncElementToTandem(element, accessToken)))

    // Mettre à jour les propriétés des éléments
    await updateTandemProperties(machineData, accessToken)

    return NextResponse.json({
      success: true,
      message: "Données synchronisées avec Tandem",
      syncResults,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error("Erreur lors de la synchronisation Tandem:", error)
    return NextResponse.json(
      {
        error: "Erreur lors de la synchronisation Tandem",
        details: error instanceof Error ? error.message : "Erreur inconnue",
      },
      { status: 500 },
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const elementId = searchParams.get("elementId")

    const accessToken = await getTandemAccessToken()

    if (!accessToken) {
      return NextResponse.json({ error: "Token Tandem manquant" }, { status: 401 })
    }

    if (elementId) {
      // Récupérer un élément spécifique
      const element = await getTandemElement(elementId, accessToken)
      return NextResponse.json({ element })
    } else {
      // Récupérer tous les éléments de la machine
      const elements = await getAllMachineElements(accessToken)
      return NextResponse.json({ elements })
    }
  } catch (error) {
    console.error("Erreur lors de la récupération Tandem:", error)
    return NextResponse.json({ error: "Erreur lors de la récupération" }, { status: 500 })
  }
}

// Fonctions utilitaires Tandem

async function getTandemAccessToken(): Promise<string | null> {
  try {
    const response = await fetch(`${TANDEM_CONFIG.baseUrl}/authentication/v2/token`, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        grant_type: "client_credentials",
        client_id: TANDEM_CONFIG.clientId!,
        client_secret: TANDEM_CONFIG.clientSecret!,
        scope: "data:read data:write",
      }),
    })

    if (!response.ok) {
      throw new Error(`Erreur d'authentification Tandem: ${response.status}`)
    }

    const data = await response.json()
    return data.access_token
  } catch (error) {
    console.error("Erreur d'authentification Tandem:", error)
    return null
  }
}

function mapMachineDataToTandem(machineData: any): TandemElement[] {
  // Mapping des données machine vers les éléments Tandem
  const elements: TandemElement[] = []

  // Élément principal de la machine
  elements.push({
    elementId: "machine-coupe-principale",
    properties: {
      "Vibration X": machineData.vibration_x,
      "Vibration Y": machineData.vibration_y,
      "Vibration Z": machineData.vibration_z,
      "État Machine": machineData.etat_machine,
      "Dernière Mise à Jour": machineData.timestamp,
      "Source Données": machineData.source,
    },
    status: getElementStatus(machineData.etat_machine),
    alerts: generateAlerts(machineData),
  })

  // Éléments des capteurs
  elements.push({
    elementId: "capteur-vibration-x",
    properties: {
      Valeur: machineData.vibration_x,
      Unité: "mm/s",
      "Seuil Alerte": 2.0,
      État: machineData.vibration_x > 2.0 ? "Alerte" : "Normal",
    },
    status: machineData.vibration_x > 2.0 ? "warning" : "normal",
    alerts: machineData.vibration_x > 2.0 ? [{ type: "vibration", message: "Vibration X élevée" }] : [],
  })

  elements.push({
    elementId: "capteur-vibration-y",
    properties: {
      Valeur: machineData.vibration_y,
      Unité: "mm/s",
      "Seuil Alerte": 2.0,
      État: machineData.vibration_y > 2.0 ? "Alerte" : "Normal",
    },
    status: machineData.vibration_y > 2.0 ? "warning" : "normal",
    alerts: machineData.vibration_y > 2.0 ? [{ type: "vibration", message: "Vibration Y élevée" }] : [],
  })

  elements.push({
    elementId: "capteur-vibration-z",
    properties: {
      Valeur: machineData.vibration_z,
      Unité: "mm/s",
      "Seuil Alerte": 2.0,
      État: machineData.vibration_z > 2.0 ? "Alerte" : "Normal",
    },
    status: machineData.vibration_z > 2.0 ? "warning" : "normal",
    alerts: machineData.vibration_z > 2.0 ? [{ type: "vibration", message: "Vibration Z élevée" }] : [],
  })

  return elements
}

function getElementStatus(etatMachine: string): string {
  switch (etatMachine) {
    case "en_marche":
      return "normal"
    case "panne":
      return "error"
    case "arret_production":
      return "stopped"
    case "probleme_qualite":
      return "warning"
    default:
      return "unknown"
  }
}

function generateAlerts(machineData: any): any[] {
  const alerts = []

  if (machineData.etat_machine === "panne") {
    alerts.push({
      type: "error",
      message: "Machine en panne",
      severity: "high",
      timestamp: machineData.timestamp,
    })
  }

  const totalVibration = Math.sqrt(
    machineData.vibration_x ** 2 + machineData.vibration_y ** 2 + machineData.vibration_z ** 2,
  )

  if (totalVibration > 4.0) {
    alerts.push({
      type: "vibration",
      message: "Vibrations critiques détectées",
      severity: "high",
      value: totalVibration,
      timestamp: machineData.timestamp,
    })
  } else if (totalVibration > 2.0) {
    alerts.push({
      type: "vibration",
      message: "Vibrations élevées",
      severity: "medium",
      value: totalVibration,
      timestamp: machineData.timestamp,
    })
  }

  return alerts
}

async function syncElementToTandem(element: TandemElement, accessToken: string) {
  try {
    const response = await fetch(
      `${TANDEM_CONFIG.baseUrl}/tandem/v1/facilities/${TANDEM_CONFIG.facilityId}/elements/${element.elementId}`,
      {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          properties: element.properties,
          status: element.status,
          alerts: element.alerts,
        }),
      },
    )

    if (!response.ok) {
      throw new Error(`Erreur de synchronisation pour ${element.elementId}: ${response.status}`)
    }

    return { elementId: element.elementId, success: true }
  } catch (error) {
    console.error(`Erreur de synchronisation pour ${element.elementId}:`, error)
    return {
      elementId: element.elementId,
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
    }
  }
}

async function updateTandemProperties(machineData: any, accessToken: string) {
  try {
    // Mise à jour des propriétés globales du modèle
    const response = await fetch(
      `${TANDEM_CONFIG.baseUrl}/tandem/v1/facilities/${TANDEM_CONFIG.facilityId}/models/${TANDEM_CONFIG.modelId}/properties`,
      {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          "Machine Status": machineData.etat_machine,
          "Last Update": machineData.timestamp,
          "Data Source": machineData.source,
          "Total Vibration": Math.sqrt(
            machineData.vibration_x ** 2 + machineData.vibration_y ** 2 + machineData.vibration_z ** 2,
          ).toFixed(3),
        }),
      },
    )

    return response.ok
  } catch (error) {
    console.error("Erreur lors de la mise à jour des propriétés:", error)
    return false
  }
}

async function getTandemElement(elementId: string, accessToken: string) {
  const response = await fetch(
    `${TANDEM_CONFIG.baseUrl}/tandem/v1/facilities/${TANDEM_CONFIG.facilityId}/elements/${elementId}`,
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    },
  )

  if (!response.ok) {
    throw new Error(`Erreur lors de la récupération de l'élément ${elementId}`)
  }

  return response.json()
}

async function getAllMachineElements(accessToken: string) {
  const response = await fetch(
    `${TANDEM_CONFIG.baseUrl}/tandem/v1/facilities/${TANDEM_CONFIG.facilityId}/elements?category=machine`,
    {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    },
  )

  if (!response.ok) {
    throw new Error("Erreur lors de la récupération des éléments machine")
  }

  return response.json()
}
