import { NextResponse } from "next/server"

// Configuration Autodesk Tandem
const TANDEM_CONFIG = {
  baseUrl: "https://developer.api.autodesk.com",
  authUrl: "https://developer.api.autodesk.com/authentication/v2/token",
  tandemApiUrl: "https://developer.api.autodesk.com/tandem/v1",
  clientId: process.env.TANDEM_CLIENT_ID,
  clientSecret: process.env.TANDEM_CLIENT_SECRET,
  facilityId: process.env.TANDEM_FACILITY_ID,
  modelId: process.env.TANDEM_MODEL_ID,
}

async function getTandemAccessToken(): Promise<string | null> {
  try {
    if (!TANDEM_CONFIG.clientId || !TANDEM_CONFIG.clientSecret) {
      throw new Error("Client ID ou Client Secret manquant")
    }

    const response = await fetch(TANDEM_CONFIG.authUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        "Accept": "application/json",
      },
      body: new URLSearchParams({
        grant_type: "client_credentials",
        client_id: TANDEM_CONFIG.clientId,
        client_secret: TANDEM_CONFIG.clientSecret,
        scope: "data:read data:write bucket:read bucket:create",
      }),
    })

    if (!response.ok) {
      throw new Error(`Erreur d'authentification: ${response.status}`)
    }

    const data = await response.json()
    return data.access_token
  } catch (error) {
    console.error("Erreur d'authentification:", error)
    return null
  }
}

async function exploreAPI(accessToken: string) {
  const results: any = {
    timestamp: new Date().toISOString(),
    endpoints: {},
    errors: []
  }

  // Liste des endpoints à tester
  const endpoints = [
    { name: "twins", url: `${TANDEM_CONFIG.tandemApiUrl}/twins` },
    { name: "facilities", url: `${TANDEM_CONFIG.tandemApiUrl}/facilities` },
    { name: "specific_facility", url: `${TANDEM_CONFIG.tandemApiUrl}/facilities/${TANDEM_CONFIG.facilityId}` },
    { name: "facility_models", url: `${TANDEM_CONFIG.tandemApiUrl}/facilities/${TANDEM_CONFIG.facilityId}/models` },
    { name: "facility_elements", url: `${TANDEM_CONFIG.tandemApiUrl}/facilities/${TANDEM_CONFIG.facilityId}/elements` },
    { name: "model_elements", url: `${TANDEM_CONFIG.tandemApiUrl}/models/${TANDEM_CONFIG.modelId}/elements` },
  ]

  for (const endpoint of endpoints) {
    try {
      console.log(`Testing endpoint: ${endpoint.name} - ${endpoint.url}`)
      
      const response = await fetch(endpoint.url, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Accept": "application/json",
        },
      })

      const result: any = {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
      }

      if (response.ok) {
        try {
          const data = await response.json()
          result.data = data
          result.success = true
        } catch (e) {
          result.data = await response.text()
          result.success = true
        }
      } else {
        try {
          result.error = await response.text()
        } catch (e) {
          result.error = `HTTP ${response.status}`
        }
        result.success = false
      }

      results.endpoints[endpoint.name] = result
    } catch (error) {
      results.endpoints[endpoint.name] = {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      }
      results.errors.push(`${endpoint.name}: ${error}`)
    }
  }

  return results
}

export async function GET() {
  try {
    console.log("=== EXPLORATION DE L'API TANDEM ===")
    
    const accessToken = await getTandemAccessToken()
    if (!accessToken) {
      return NextResponse.json({
        success: false,
        error: "Impossible d'obtenir le token d'authentification"
      }, { status: 401 })
    }

    console.log("Token obtenu, exploration des endpoints...")
    const results = await exploreAPI(accessToken)
    
    return NextResponse.json({
      success: true,
      message: "Exploration terminée",
      config: {
        facilityId: TANDEM_CONFIG.facilityId,
        modelId: TANDEM_CONFIG.modelId,
        tandemApiUrl: TANDEM_CONFIG.tandemApiUrl,
      },
      results
    })
  } catch (error) {
    console.error("Erreur lors de l'exploration:", error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue"
    }, { status: 500 })
  }
}
