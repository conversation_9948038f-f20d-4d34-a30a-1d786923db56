import { type NextRequest, NextResponse } from "next/server"

// Endpoint spécialement conçu pour Node-RED
export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    console.log("Données reçues de Node-RED:", data)

    // Rediriger vers l'API principale avec le bon format
    const response = await fetch(`${request.nextUrl.origin}/api/data`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    })

    const result = await response.json()

    return NextResponse.json({
      success: true,
      message: "Données Node-RED traitées avec succès",
      result,
    })
  } catch (error) {
    console.error("Erreur Node-RED:", error)
    return NextResponse.json(
      {
        error: "Erreur lors du traitement des données Node-RED",
        details: error instanceof Error ? error.message : "Erreur inconnue",
      },
      { status: 500 },
    )
  }
}

export async function GET() {
  return NextResponse.json({
    status: "active",
    endpoint: "Node-RED Data Receiver",
    timestamp: new Date().toISOString(),
    supportedMethods: ["POST"],
    expectedFormat: {
      payload: {
        vibration_data: {
          x: "number",
          y: "number",
          z: "number",
        },
        machine_state: "string (optional)",
        additional_data: "object (optional)",
      },
      topic: "string (optional)",
    },
  })
}
