import { NextResponse } from "next/server"

// Configuration Autodesk Tandem
const TANDEM_CONFIG = {
  baseUrl: "https://developer.api.autodesk.com",
  authUrl: "https://developer.api.autodesk.com/authentication/v2/token",
  tandemApiUrl: "https://developer.api.autodesk.com/tandem/v1",
  clientId: process.env.TANDEM_CLIENT_ID,
  clientSecret: process.env.TANDEM_CLIENT_SECRET,
  facilityId: process.env.TANDEM_FACILITY_ID,
  modelId: process.env.TANDEM_MODEL_ID,
}

async function testTandemAuth() {
  try {
    console.log("=== TEST D'AUTHENTIFICATION TANDEM ===")
    console.log("Client ID:", TANDEM_CONFIG.clientId ? "✓ Défini" : "✗ Manquant")
    console.log("Client Secret:", TANDEM_CONFIG.clientSecret ? "✓ Défini" : "✗ Manquant")
    console.log("Facility ID:", TANDEM_CONFIG.facilityId ? "✓ Défini" : "✗ Manquant")
    console.log("Model ID:", TANDEM_CONFIG.modelId ? "✓ Défini" : "✗ Manquant")
    console.log("URL d'authentification:", TANDEM_CONFIG.authUrl)

    if (!TANDEM_CONFIG.clientId || !TANDEM_CONFIG.clientSecret) {
      return {
        success: false,
        error: "Client ID ou Client Secret manquant",
        config: {
          clientId: !!TANDEM_CONFIG.clientId,
          clientSecret: !!TANDEM_CONFIG.clientSecret,
          facilityId: !!TANDEM_CONFIG.facilityId,
          modelId: !!TANDEM_CONFIG.modelId,
        }
      }
    }

    const response = await fetch(TANDEM_CONFIG.authUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        "Accept": "application/json",
      },
      body: new URLSearchParams({
        grant_type: "client_credentials",
        client_id: TANDEM_CONFIG.clientId,
        client_secret: TANDEM_CONFIG.clientSecret,
        scope: "data:read data:write bucket:read bucket:create",
      }),
    })

    console.log("Réponse d'authentification:", response.status, response.statusText)

    if (!response.ok) {
      const errorText = await response.text()
      console.error("Erreur d'authentification détaillée:", errorText)
      
      return {
        success: false,
        error: `Erreur d'authentification: ${response.status}`,
        details: errorText,
        config: {
          clientId: !!TANDEM_CONFIG.clientId,
          clientSecret: !!TANDEM_CONFIG.clientSecret,
          facilityId: !!TANDEM_CONFIG.facilityId,
          modelId: !!TANDEM_CONFIG.modelId,
        }
      }
    }

    const data = await response.json()
    console.log("Authentification réussie!")
    
    return {
      success: true,
      message: "Authentification Tandem réussie",
      tokenType: data.token_type,
      expiresIn: data.expires_in,
      config: {
        clientId: !!TANDEM_CONFIG.clientId,
        clientSecret: !!TANDEM_CONFIG.clientSecret,
        facilityId: !!TANDEM_CONFIG.facilityId,
        modelId: !!TANDEM_CONFIG.modelId,
      }
    }
  } catch (error) {
    console.error("Erreur lors du test d'authentification:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
      config: {
        clientId: !!TANDEM_CONFIG.clientId,
        clientSecret: !!TANDEM_CONFIG.clientSecret,
        facilityId: !!TANDEM_CONFIG.facilityId,
        modelId: !!TANDEM_CONFIG.modelId,
      }
    }
  }
}

export async function GET() {
  const result = await testTandemAuth()
  
  return NextResponse.json(result, { 
    status: result.success ? 200 : 400 
  })
}
