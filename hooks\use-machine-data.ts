"use client"

import { useState, useEffect, useCallback } from "react"

interface MachineData {
  timestamp: string
  vibration_x: number
  vibration_y: number
  vibration_z: number
  etat_machine: string
}

export function useMachineData(hours = 6) {
  const [data, setData] = useState<MachineData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/firebase?hours=${hours}&limit=1000`)

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`)
      }

      const result = await response.json()

      if (result.error) {
        throw new Error(result.error)
      }

      setData(result.data || [])
    } catch (err) {
      console.error("Erreur lors de la récupération des données:", err)
      setError(err instanceof Error ? err.message : "Erreur inconnue")
    } finally {
      setLoading(false)
    }
  }, [hours])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return { data, loading, error, refetch: fetchData }
}
