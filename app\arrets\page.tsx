"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { AlertTriangle, Plus, Clock, User, FileText } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

const typesArrets = {
  arret_maintenance: {
    label: "Arrêt Maintenance",
    color: "#1f4e79",
    icon: "🔧",
    sous_categories: ["Maintenance préventive", "Maintenance corrective", "Changement d'outil", "Nettoyage"],
  },
  arret_prevu: {
    label: "Arrêt P<PERSON>vu",
    color: "#40e0d0",
    icon: "📅",
    sous_categories: ["Pause déjeuner", "Fin d'équipe", "Changement d'équipe", "Formation"],
  },
  arret_imprevu: {
    label: "Arrêt Imprévu",
    color: "#9932cc",
    icon: "⚡",
    sous_categories: ["Panne électrique", "Panne mécanique", "Problème matière première", "Incident sécurité"],
  },
  arret_conducteur: {
    label: "Arrêt Conducteur",
    color: "#ffd700",
    icon: "👤",
    sous_categories: ["Absence opérateur", "Formation opérateur", "Réglage machine", "Contrôle qualité"],
  },
  arret_qualite: {
    label: "Arrêt Qualité",
    color: "#ff0000",
    icon: "🎯",
    sous_categories: ["Défaut produit", "Contrôle qualité", "Rejet client", "Non-conformité"],
  },
}

const niveauxUrgence = ["Faible", "Moyen", "Élevé", "Critique"]
const composantsMachine = [
  "Moteur principal",
  "Système de coupe",
  "Convoyeur",
  "Système hydraulique",
  "Capteurs",
  "Interface utilisateur",
  "Autre",
]

export default function ArretsPage() {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    type_arret: "",
    sous_categorie: "",
    piece_concernee: "",
    duree_minutes: 10,
    commentaire: "",
    operateur: "",
    urgence: "Moyen",
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      // Simulation de sauvegarde
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "Arrêt enregistré",
        description: "L'arrêt a été enregistré avec succès.",
      })

      // Reset du formulaire
      setFormData({
        type_arret: "",
        sous_categorie: "",
        piece_concernee: "",
        duree_minutes: 10,
        commentaire: "",
        operateur: "",
        urgence: "Moyen",
      })
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Erreur lors de l'enregistrement de l'arrêt.",
        variant: "destructive",
      })
    }
  }

  const selectedType = typesArrets[formData.type_arret as keyof typeof typesArrets]

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Saisie des Causes d'Arrêt</h2>
          <p className="text-muted-foreground">Enregistrement et suivi des arrêts de production</p>
        </div>
      </div>

      <Tabs defaultValue="nouveau" className="space-y-4">
        <TabsList>
          <TabsTrigger value="nouveau">🔧 Nouveau Rapport</TabsTrigger>
          <TabsTrigger value="recents">📋 Arrêts Récents</TabsTrigger>
          <TabsTrigger value="statistiques">📊 Statistiques</TabsTrigger>
        </TabsList>

        <TabsContent value="nouveau" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="h-5 w-5" />
                  Nouveau Rapport d'Arrêt
                </CardTitle>
                <CardDescription>Saisissez les informations concernant l'arrêt de production</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="date">Date de l'arrêt</Label>
                      <Input id="date" type="date" defaultValue={new Date().toISOString().split("T")[0]} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="heure">Heure de l'arrêt</Label>
                      <Input id="heure" type="time" defaultValue={new Date().toTimeString().slice(0, 5)} />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="type_arret">Type d'arrêt</Label>
                    <Select
                      value={formData.type_arret}
                      onValueChange={(value) => setFormData({ ...formData, type_arret: value, sous_categorie: "" })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionnez le type d'arrêt" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(typesArrets).map(([key, type]) => (
                          <SelectItem key={key} value={key}>
                            <div className="flex items-center gap-2">
                              <span>{type.icon}</span>
                              <span>{type.label}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedType && (
                    <div className="space-y-2">
                      <Label htmlFor="sous_categorie">Sous-catégorie</Label>
                      <Select
                        value={formData.sous_categorie}
                        onValueChange={(value) => setFormData({ ...formData, sous_categorie: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionnez la sous-catégorie" />
                        </SelectTrigger>
                        <SelectContent>
                          {selectedType.sous_categories.map((cat) => (
                            <SelectItem key={cat} value={cat}>
                              {cat}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="piece_concernee">Pièce concernée (optionnel)</Label>
                    <Select
                      value={formData.piece_concernee}
                      onValueChange={(value) => setFormData({ ...formData, piece_concernee: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionnez la pièce concernée" />
                      </SelectTrigger>
                      <SelectContent>
                        {composantsMachine.map((composant) => (
                          <SelectItem key={composant} value={composant}>
                            {composant}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="duree">Durée de l'arrêt (minutes)</Label>
                    <Input
                      id="duree"
                      type="number"
                      min="1"
                      value={formData.duree_minutes}
                      onChange={(e) => setFormData({ ...formData, duree_minutes: Number.parseInt(e.target.value) })}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="operateur">Nom de l'opérateur</Label>
                    <Input
                      id="operateur"
                      value={formData.operateur}
                      onChange={(e) => setFormData({ ...formData, operateur: e.target.value })}
                      placeholder="Nom de l'opérateur"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="urgence">Niveau d'urgence</Label>
                    <Select
                      value={formData.urgence}
                      onValueChange={(value) => setFormData({ ...formData, urgence: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {niveauxUrgence.map((niveau) => (
                          <SelectItem key={niveau} value={niveau}>
                            {niveau}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="commentaire">Commentaire détaillé</Label>
                    <Textarea
                      id="commentaire"
                      value={formData.commentaire}
                      onChange={(e) => setFormData({ ...formData, commentaire: e.target.value })}
                      placeholder="Décrivez les circonstances de l'arrêt..."
                      rows={4}
                    />
                  </div>

                  <Button type="submit" className="w-full">
                    💾 Enregistrer l'arrêt
                  </Button>
                </form>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>🎨 Classification des Arrêts</CardTitle>
                <CardDescription>Types d'arrêts disponibles avec leurs couleurs</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(typesArrets).map(([key, type]) => (
                  <div
                    key={key}
                    className="p-4 rounded-lg border-l-4 bg-muted/50"
                    style={{ borderLeftColor: type.color }}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-lg">{type.icon}</span>
                      <span className="font-semibold" style={{ color: type.color }}>
                        {type.label}
                      </span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      <strong>Sous-catégories:</strong> {type.sous_categories.slice(0, 3).join(", ")}
                      {type.sous_categories.length > 3 && "..."}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="recents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>📋 Derniers Arrêts Enregistrés</CardTitle>
              <CardDescription>Liste des arrêts récemment enregistrés</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Exemple d'arrêts récents */}
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="destructive">🔧 Maintenance</Badge>
                      <span className="text-sm text-muted-foreground">
                        {new Date().toLocaleDateString()} - {new Date().toLocaleTimeString()}
                      </span>
                    </div>
                    <Badge variant="outline">15 min</Badge>
                  </div>
                  <div className="grid gap-2 md:grid-cols-3 text-sm">
                    <div>
                      <span className="font-medium">Type:</span> Maintenance préventive
                    </div>
                    <div>
                      <span className="font-medium">Pièce:</span> Moteur principal
                    </div>
                    <div>
                      <span className="font-medium">Opérateur:</span> Jean Dupont
                    </div>
                  </div>
                  <div className="mt-2 text-sm text-muted-foreground">
                    <span className="font-medium">Commentaire:</span> Maintenance préventive programmée - Changement des
                    filtres
                  </div>
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">⚡ Imprévu</Badge>
                      <span className="text-sm text-muted-foreground">
                        {new Date(Date.now() - 3600000).toLocaleDateString()} -{" "}
                        {new Date(Date.now() - 3600000).toLocaleTimeString()}
                      </span>
                    </div>
                    <Badge variant="outline">8 min</Badge>
                  </div>
                  <div className="grid gap-2 md:grid-cols-3 text-sm">
                    <div>
                      <span className="font-medium">Type:</span> Panne électrique
                    </div>
                    <div>
                      <span className="font-medium">Pièce:</span> Capteurs
                    </div>
                    <div>
                      <span className="font-medium">Opérateur:</span> Marie Martin
                    </div>
                  </div>
                  <div className="mt-2 text-sm text-muted-foreground">
                    <span className="font-medium">Commentaire:</span> Capteur de vibration défaillant - Remplacé
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistiques" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total arrêts</p>
                    <p className="text-2xl font-bold">24</p>
                  </div>
                  <FileText className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Durée totale</p>
                    <p className="text-2xl font-bold">180 min</p>
                  </div>
                  <Clock className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Durée moyenne</p>
                    <p className="text-2xl font-bold">7.5 min</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Aujourd'hui</p>
                    <p className="text-2xl font-bold">3</p>
                  </div>
                  <User className="h-8 w-8 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Répartition par Type</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(typesArrets).map(([key, type]) => (
                    <div key={key} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: type.color }} />
                        <span className="text-sm">{type.label}</span>
                      </div>
                      <Badge variant="outline">{Math.floor(Math.random() * 10) + 1}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Durée par Type</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(typesArrets).map(([key, type]) => (
                    <div key={key} className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>{type.label}</span>
                        <span>{Math.floor(Math.random() * 60) + 10} min</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div
                          className="h-2 rounded-full"
                          style={{
                            backgroundColor: type.color,
                            width: `${Math.floor(Math.random() * 80) + 20}%`,
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
